# AI Brand Monitor - Migration Guide to OpenRouter API

## Overview

This guide covers the migration from web scraper-based monitoring to OpenRouter API-based monitoring. The new system provides more reliable, scalable, and cost-effective brand monitoring across multiple AI models.

## Key Changes

### 1. Architecture Changes

**Before (Scrapers):**
- Selenium-based web scrapers for ChatGPT, Claude, Perplexity
- Browser automation with anti-detection measures
- High resource usage and reliability issues
- Limited to specific AI platforms

**After (OpenRouter API):**
- Direct API integration with OpenRouter
- Access to 20+ AI models from different providers
- Reliable, fast, and scalable
- Token-based usage tracking and cost management

### 2. New Components

#### OpenRouter Client (`app/clients/openrouter_client.py`)
- Handles API communication with OpenRouter
- Rate limiting and retry logic
- Cost calculation and usage tracking
- Support for multiple AI models

#### AI Model Monitor (`app/clients/ai_model_monitor.py`)
- Replaces scraper-based monitoring
- Uses OpenRouter API for queries
- Maintains compatibility with existing analyzers
- Enhanced error handling and logging

#### Page Analyzer (`app/analyzers/page_analyzer.py`)
- AI-powered website analysis
- Automatic keyword extraction
- Competitor identification
- Query generation for monitoring

#### Model Management (`app/admin/model_management.py`)
- Admin interface for AI model configuration
- Usage statistics and cost monitoring
- Model enabling/disabling
- Tier-based access control

#### API Usage Tracker (`app/utils/api_usage_tracker.py`)
- Track API usage per user and model
- Quota management and limits
- Cost monitoring and alerts
- Usage analytics

### 3. Database Schema Updates

#### New Tables:
- `ai_models` - Configuration for available AI models
- `api_usage` - Track API calls, tokens, and costs
- `page_analyses` - Store website analysis results

#### Updated Tables:
- `users` - Added OpenRouter API key and usage tier fields
- `ai_responses` - Added model information and cost tracking

### 4. Configuration Changes

#### New Environment Variables:
```bash
# OpenRouter API Configuration
OPENROUTER_API_KEY=your-openrouter-api-key
OPENROUTER_DEFAULT_MODEL=openai/gpt-3.5-turbo
OPENROUTER_RATE_LIMIT=60
OPENROUTER_MAX_TOKENS=1000
OPENROUTER_TEMPERATURE=0.7
APP_URL=http://localhost:8000

# User API Limits
FREE_TIER_DAILY_LIMIT=50
PREMIUM_TIER_DAILY_LIMIT=500
ENTERPRISE_TIER_DAILY_LIMIT=2000
```

## Migration Steps

### 1. Update Dependencies

Install new dependencies:
```bash
pip install httpx aiohttp asyncio-throttle
```

### 2. Database Migration

The new models will be automatically created when you start the application. The migration script will:
- Create new tables for AI models, API usage, and page analyses
- Add new fields to existing tables
- Populate default AI models

### 3. Configuration Setup

1. Copy `.env.example` to `.env`
2. Add your OpenRouter API key
3. Configure user tier limits as needed
4. Set application URL for OpenRouter integration

### 4. Admin Setup

1. Access admin panel at `/admin` (admin users only)
2. Sync available models from OpenRouter
3. Configure model settings and limits
4. Set up user tiers and quotas

### 5. User Migration

Existing users need to:
1. Add their OpenRouter API key in settings
2. Test API connection
3. Configure monitoring preferences
4. Migrate from scraper-based to API-based monitoring

## New Features

### 1. Multi-Model Monitoring
- Monitor brand across 20+ AI models
- Compare responses between different models
- Model-specific analytics and insights

### 2. Intelligent Page Analysis
- Automatic website analysis using AI
- Extract keywords, competitors, products
- Generate monitoring queries automatically
- Brand positioning analysis

### 3. Advanced Usage Management
- Per-user API quotas and limits
- Cost tracking and budget management
- Usage analytics and reporting
- Tier-based access control

### 4. Enhanced Admin Panel
- Model configuration and management
- Usage statistics and monitoring
- User management and limits
- Cost analysis and optimization

### 5. Improved Scheduling
- Optimized for API calls vs web scraping
- Intelligent quota management
- Parallel processing for multiple users
- Better error handling and recovery

## API Changes

### New Endpoints

#### OpenRouter Integration:
- `GET /api/v1/openrouter/models` - Get available models
- `POST /api/v1/openrouter/test-connection` - Test API connection

#### Brand Management:
- `POST /api/v1/brands/{id}/analyze-page` - Analyze website
- `POST /api/v1/brands/{id}/monitor-ai` - Start AI monitoring

#### User Management:
- `GET /api/v1/users/me/api-usage` - Get usage statistics
- `PUT /api/v1/users/me/openrouter-key` - Update API key

#### Admin Endpoints:
- `GET /api/v1/admin/models` - Manage AI models
- `POST /api/v1/admin/models/sync` - Sync models from OpenRouter
- `PUT /api/v1/admin/models/{id}` - Update model configuration
- `GET /api/v1/admin/usage-stats` - Usage statistics
- `GET /api/v1/admin/users-usage` - User usage summary

## Cost Considerations

### OpenRouter Pricing
- Pay-per-use model based on tokens
- Different costs for different models
- Transparent pricing and usage tracking

### Cost Optimization Tips
1. Use cheaper models for routine monitoring
2. Set appropriate daily/monthly limits
3. Monitor usage regularly
4. Use model-specific limits for different user tiers

## Backward Compatibility

### Maintained Features:
- All existing API endpoints continue to work
- Database schema is backward compatible
- Existing analytics and reporting
- User authentication and authorization

### Deprecated Features:
- Web scraper endpoints (will be removed in future version)
- Platform-specific scraper configurations
- Browser automation settings

## Troubleshooting

### Common Issues:

1. **API Key Issues**
   - Verify OpenRouter API key is valid
   - Check API key permissions
   - Test connection using the test endpoint

2. **Quota Exceeded**
   - Check daily/monthly limits
   - Review usage statistics
   - Adjust limits or upgrade tier

3. **Model Not Available**
   - Sync models from OpenRouter
   - Check model is enabled
   - Verify model ID is correct

4. **High Costs**
   - Review model selection (use cheaper models)
   - Set appropriate limits
   - Monitor usage patterns

### Support

For issues or questions:
1. Check the logs for detailed error messages
2. Review usage statistics in admin panel
3. Test API connection and model availability
4. Contact support with specific error details

## Future Enhancements

### Planned Features:
- Real-time monitoring dashboard
- Advanced analytics and insights
- Integration with more AI platforms
- Custom model fine-tuning
- Advanced alerting and notifications
- Mobile app for monitoring

### Performance Improvements:
- Caching for frequently used queries
- Batch processing for multiple brands
- Optimized database queries
- Enhanced error recovery
