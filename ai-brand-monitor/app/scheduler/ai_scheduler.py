import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from loguru import logger

from app.database.database import get_db_session
from app.database import models
from app.clients.ai_model_monitor import AIModelMonitor
from app.analyzers.page_analyzer import PageAnalyzer
from app.utils.api_usage_tracker import APIUsageTracker
from app.admin.model_management import ModelManager
from app.notifications.email_sender import EmailSender
from app.config import config


class AIBrandMonitorScheduler:
    """Enhanced scheduler for AI-based brand monitoring"""
    
    def __init__(self):
        self.scheduler = AsyncIOScheduler()
        self.ai_monitor = AIModelMonitor()
        self.page_analyzer = PageAnalyzer()
        self.model_manager = ModelManager()
        self.email_sender = EmailSender()
        
    def start(self):
        """Start the scheduler with AI monitoring jobs"""
        
        # Daily AI monitoring for all active brands
        self.scheduler.add_job(
            self.daily_ai_monitoring,
            CronTrigger.from_crontab(config.SCAN_SCHEDULE),
            id='daily_ai_monitoring',
            replace_existing=True
        )
        
        # Weekly report generation
        self.scheduler.add_job(
            self.weekly_report_generation,
            CronTrigger.from_crontab(config.REPORT_SCHEDULE),
            id='weekly_reports',
            replace_existing=True
        )
        
        # Hourly usage monitoring and alerts
        self.scheduler.add_job(
            self.monitor_api_usage,
            'interval',
            hours=1,
            id='usage_monitoring',
            replace_existing=True
        )
        
        # Daily model sync (check for new models)
        self.scheduler.add_job(
            self.sync_ai_models,
            'cron',
            hour=2,
            minute=0,
            id='model_sync',
            replace_existing=True
        )
        
        # Weekly cleanup of old data
        self.scheduler.add_job(
            self.cleanup_old_data,
            'cron',
            day_of_week='sun',
            hour=3,
            minute=0,
            id='weekly_cleanup',
            replace_existing=True
        )
        
        self.scheduler.start()
        logger.info("AI Brand Monitor Scheduler started successfully")
    
    def stop(self):
        """Stop the scheduler"""
        self.scheduler.shutdown()
        logger.info("AI Brand Monitor Scheduler stopped")
    
    async def daily_ai_monitoring(self):
        """Perform daily AI monitoring for all active brands"""
        logger.info("Starting daily AI monitoring")
        
        with get_db_session() as db:
            # Get all active brands with users who have API keys
            active_brands = db.query(models.Brand).filter(
                models.Brand.is_active == True
            ).all()
            
            for brand in active_brands:
                try:
                    # Get users with API access for this brand
                    brand_users = db.query(models.User).join(
                        models.UserBrand
                    ).filter(
                        models.UserBrand.brand_id == brand.id,
                        models.User.openrouter_api_key.isnot(None),
                        models.User.is_active == True
                    ).all()
                    
                    if not brand_users:
                        logger.warning(f"No users with API keys found for brand {brand.name}")
                        continue
                    
                    # Use the first available user's API key
                    user = brand_users[0]
                    
                    # Generate queries for monitoring
                    queries = await self._generate_monitoring_queries(brand, db)
                    
                    if not queries:
                        logger.warning(f"No queries generated for brand {brand.name}")
                        continue
                    
                    # Get enabled models for monitoring
                    enabled_models = db.query(models.AIModel).filter(
                        models.AIModel.is_enabled == True
                    ).all()
                    
                    models_to_use = [model.model_id for model in enabled_models[:3]]  # Limit to 3 models
                    
                    # Check user limits
                    total_requests = len(queries) * len(models_to_use)
                    can_proceed = True
                    
                    for model in enabled_models[:3]:
                        limits_check = APIUsageTracker.check_user_limits(
                            user.id, model.id, len(queries)
                        )
                        if not limits_check["allowed"]:
                            logger.warning(f"User {user.email} exceeded limits for {model.name}")
                            can_proceed = False
                            break
                    
                    if not can_proceed:
                        continue
                    
                    # Perform monitoring
                    monitor = AIModelMonitor(user.openrouter_api_key)
                    results = await monitor.monitor_brand(
                        brand=brand,
                        queries=queries,
                        models_to_use=models_to_use,
                        user_id=user.id
                    )
                    
                    # Save analytics
                    await self._save_daily_analytics(brand, results, db)
                    
                    # Check for alerts
                    await self._check_and_create_alerts(brand, results, db)
                    
                    logger.info(f"Completed monitoring for brand {brand.name}: {len(results['mentions'])} mentions found")
                    
                except Exception as e:
                    logger.error(f"Error monitoring brand {brand.name}: {str(e)}")
                    
                    # Create alert for monitoring failure
                    alert = models.Alert(
                        brand_id=brand.id,
                        alert_type='monitoring_failure',
                        severity='high',
                        title=f"Daily monitoring failed for {brand.name}",
                        message=f"Error: {str(e)}"
                    )
                    db.add(alert)
                    db.commit()
    
    async def _generate_monitoring_queries(self, brand: models.Brand, db) -> List[str]:
        """Generate monitoring queries for a brand"""
        
        queries = []
        
        # Get queries from page analysis if available
        page_analysis = db.query(models.PageAnalysis).filter(
            models.PageAnalysis.brand_id == brand.id
        ).first()
        
        if page_analysis and page_analysis.suggested_queries:
            queries.extend(page_analysis.suggested_queries[:8])
        
        # Generate additional queries if needed
        if len(queries) < 5:
            try:
                additional_queries = await self.page_analyzer.generate_additional_queries(
                    brand_name=brand.name,
                    industry=brand.industry or "business",
                    keywords=brand.keywords or [],
                    competitors=brand.competitors or [],
                    query_count=8 - len(queries)
                )
                queries.extend(additional_queries)
            except Exception as e:
                logger.error(f"Error generating additional queries for {brand.name}: {str(e)}")
        
        # Fallback to basic queries if still not enough
        if len(queries) < 3:
            basic_queries = [
                f"What are the best {brand.industry or 'business'} solutions?",
                f"Can you recommend companies in the {brand.industry or 'business'} industry?",
                f"What should I know about {brand.name}?"
            ]
            queries.extend(basic_queries)
        
        return queries[:10]  # Limit to 10 queries
    
    async def _save_daily_analytics(self, brand: models.Brand, results: Dict, db):
        """Save daily analytics from monitoring results"""
        
        analytics = models.Analytics(
            brand_id=brand.id,
            date=datetime.utcnow(),
            visibility_score=results["analytics"].get("visibility_score", 0.0),
            sentiment_score=results["analytics"].get("sentiment_score", 0.0),
            authority_score=results["analytics"].get("authority_score", 0.0),
            total_mentions=results["analytics"].get("total_mentions", 0),
            positive_mentions=results["analytics"].get("positive_mentions", 0),
            negative_mentions=results["analytics"].get("negative_mentions", 0),
            neutral_mentions=results["analytics"].get("neutral_mentions", 0),
            platform_breakdown=results["analytics"].get("models_coverage", {}),
            top_keywords=[],  # Could be extracted from mentions
            competitor_comparison={}  # Could be calculated from competitor mentions
        )
        
        db.add(analytics)
        db.commit()
    
    async def _check_and_create_alerts(self, brand: models.Brand, results: Dict, db):
        """Check monitoring results and create alerts if needed"""
        
        analytics = results["analytics"]
        
        # Check for significant drops in visibility
        recent_analytics = db.query(models.Analytics).filter(
            models.Analytics.brand_id == brand.id
        ).order_by(models.Analytics.date.desc()).limit(7).all()
        
        if len(recent_analytics) >= 2:
            current_visibility = analytics.get("visibility_score", 0)
            previous_visibility = recent_analytics[1].visibility_score
            
            if current_visibility < previous_visibility - 20:  # 20 point drop
                alert = models.Alert(
                    brand_id=brand.id,
                    alert_type='visibility_drop',
                    severity='medium',
                    title=f"Visibility drop detected for {brand.name}",
                    message=f"Visibility score dropped from {previous_visibility:.1f} to {current_visibility:.1f}",
                    data={"current": current_visibility, "previous": previous_visibility}
                )
                db.add(alert)
        
        # Check for negative sentiment spike
        negative_mentions = analytics.get("negative_mentions", 0)
        total_mentions = analytics.get("total_mentions", 0)
        
        if total_mentions > 0 and (negative_mentions / total_mentions) > 0.3:  # >30% negative
            alert = models.Alert(
                brand_id=brand.id,
                alert_type='negative_sentiment',
                severity='high',
                title=f"High negative sentiment for {brand.name}",
                message=f"{negative_mentions} out of {total_mentions} mentions were negative",
                data={"negative_mentions": negative_mentions, "total_mentions": total_mentions}
            )
            db.add(alert)
        
        db.commit()
    
    async def weekly_report_generation(self):
        """Generate weekly reports for all brands"""
        logger.info("Starting weekly report generation")
        
        with get_db_session() as db:
            brands = db.query(models.Brand).filter(models.Brand.is_active == True).all()
            
            for brand in brands:
                try:
                    # Get users to send reports to
                    users = db.query(models.User).join(
                        models.UserBrand
                    ).filter(
                        models.UserBrand.brand_id == brand.id,
                        models.UserBrand.role.in_(['admin', 'editor'])
                    ).all()
                    
                    if not users:
                        continue
                    
                    # Generate report data
                    report_data = await self._generate_weekly_report(brand, db)
                    
                    # Send email reports
                    for user in users:
                        await self.email_sender.send_weekly_report(
                            user.email,
                            brand.name,
                            report_data
                        )
                    
                    logger.info(f"Weekly report sent for brand {brand.name}")
                    
                except Exception as e:
                    logger.error(f"Error generating weekly report for {brand.name}: {str(e)}")
    
    async def _generate_weekly_report(self, brand: models.Brand, db) -> Dict:
        """Generate weekly report data"""
        
        week_ago = datetime.utcnow() - timedelta(days=7)
        
        # Get analytics for the past week
        analytics = db.query(models.Analytics).filter(
            models.Analytics.brand_id == brand.id,
            models.Analytics.date >= week_ago
        ).order_by(models.Analytics.date.desc()).all()
        
        if not analytics:
            return {"error": "No data available for the past week"}
        
        # Calculate averages
        avg_visibility = sum(a.visibility_score for a in analytics) / len(analytics)
        avg_sentiment = sum(a.sentiment_score for a in analytics) / len(analytics)
        total_mentions = sum(a.total_mentions for a in analytics)
        
        # Calculate trend
        if len(analytics) >= 2:
            recent_avg = sum(a.visibility_score for a in analytics[:3]) / min(3, len(analytics))
            older_avg = sum(a.visibility_score for a in analytics[-3:]) / min(3, len(analytics))
            trend = "improving" if recent_avg > older_avg else "declining" if recent_avg < older_avg else "stable"
        else:
            trend = "insufficient_data"
        
        return {
            "brand": brand.name,
            "period": {"start": week_ago, "end": datetime.utcnow()},
            "metrics": {
                "avg_visibility": round(avg_visibility, 1),
                "avg_sentiment": round(avg_sentiment, 1),
                "total_mentions": total_mentions,
                "trend": trend
            },
            "daily_scores": [
                {
                    "date": a.date.strftime('%Y-%m-%d'),
                    "visibility": a.visibility_score,
                    "sentiment": a.sentiment_score,
                    "mentions": a.total_mentions
                } for a in analytics
            ]
        }
    
    async def monitor_api_usage(self):
        """Monitor API usage and send alerts for users approaching limits"""
        logger.info("Monitoring API usage")
        
        try:
            # Get cost alerts
            alerts = APIUsageTracker.get_cost_alerts(threshold_percent=80.0)
            
            for alert in alerts:
                # Send email notification
                await self.email_sender.send_usage_alert(
                    alert["email"],
                    alert["alert_type"],
                    alert["utilization_percent"],
                    alert["monthly_budget"],
                    alert["monthly_spent"]
                )
                
                logger.info(f"Usage alert sent to {alert['email']}")
                
        except Exception as e:
            logger.error(f"Error monitoring API usage: {str(e)}")
    
    async def sync_ai_models(self):
        """Sync available AI models from OpenRouter"""
        logger.info("Syncing AI models")
        
        try:
            result = await self.model_manager.sync_available_models()
            logger.info(f"Model sync completed: {result}")
        except Exception as e:
            logger.error(f"Error syncing AI models: {str(e)}")
    
    async def cleanup_old_data(self):
        """Clean up old data to save space"""
        logger.info("Starting weekly data cleanup")
        
        try:
            # Clean up old API usage records (keep 90 days)
            deleted_usage = APIUsageTracker.cleanup_old_usage_records(days_to_keep=90)
            
            # Clean up old analytics (keep 1 year)
            with get_db_session() as db:
                cutoff_date = datetime.utcnow() - timedelta(days=365)
                deleted_analytics = db.query(models.Analytics).filter(
                    models.Analytics.date < cutoff_date
                ).delete()
                
                # Clean up old alerts (keep 30 days)
                alert_cutoff = datetime.utcnow() - timedelta(days=30)
                deleted_alerts = db.query(models.Alert).filter(
                    models.Alert.created_at < alert_cutoff,
                    models.Alert.is_resolved == True
                ).delete()
                
                db.commit()
            
            logger.info(f"Cleanup completed: {deleted_usage} usage records, {deleted_analytics} analytics, {deleted_alerts} alerts")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {str(e)}")


# Global scheduler instance
ai_scheduler = AIBrandMonitorScheduler()
