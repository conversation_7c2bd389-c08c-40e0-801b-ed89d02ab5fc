from datetime import datetime
from .database import get_db_session, init_db
from .models import User, <PERSON>, AIModel
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def create_initial_data():
    """Create initial data for testing"""
    with get_db_session() as db:
        # Create admin user
        admin = User(
            email="<EMAIL>",
            hashed_password=pwd_context.hash("admin123"),
            full_name="System Admin",
            is_admin=True,
            is_active=True
        )
        db.add(admin)
        
        # Create sample brands
        brands = [
            Brand(
                name="Nike",
                description="Global sports apparel brand",
                industry="Sports & Fashion",
                keywords=["nike", "just do it", "nike shoes", "nike sneakers"],
                competitors=["Adidas", "Puma", "Under Armour", "New Balance"]
            ),
            Brand(
                name="Apple",
                description="Technology company",
                industry="Technology",
                keywords=["apple", "iphone", "macbook", "ipad", "apple products"],
                competitors=["Samsung", "Google", "Microsoft", "Sony"]
            )
        ]
        
        for brand in brands:
            db.add(brand)

        # Create default AI models
        default_models = [
            AIModel(
                model_id="openai/gpt-3.5-turbo",
                name="GPT-3.5 Turbo",
                provider="OpenAI",
                context_length=4096,
                cost_per_1k_prompt_tokens=0.0015,
                cost_per_1k_completion_tokens=0.002,
                is_enabled=True,
                is_default=True,
                free_tier_daily_limit=10,
                premium_tier_daily_limit=100,
                enterprise_tier_daily_limit=500
            ),
            AIModel(
                model_id="openai/gpt-4",
                name="GPT-4",
                provider="OpenAI",
                context_length=8192,
                cost_per_1k_prompt_tokens=0.03,
                cost_per_1k_completion_tokens=0.06,
                is_enabled=True,
                is_default=False,
                free_tier_daily_limit=5,
                premium_tier_daily_limit=50,
                enterprise_tier_daily_limit=200
            ),
            AIModel(
                model_id="anthropic/claude-3-haiku",
                name="Claude 3 Haiku",
                provider="Anthropic",
                context_length=200000,
                cost_per_1k_prompt_tokens=0.00025,
                cost_per_1k_completion_tokens=0.00125,
                is_enabled=True,
                is_default=False,
                free_tier_daily_limit=15,
                premium_tier_daily_limit=150,
                enterprise_tier_daily_limit=600
            ),
            AIModel(
                model_id="anthropic/claude-3-sonnet",
                name="Claude 3 Sonnet",
                provider="Anthropic",
                context_length=200000,
                cost_per_1k_prompt_tokens=0.003,
                cost_per_1k_completion_tokens=0.015,
                is_enabled=True,
                is_default=False,
                free_tier_daily_limit=8,
                premium_tier_daily_limit=80,
                enterprise_tier_daily_limit=300
            )
        ]

        for model in default_models:
            db.add(model)

        db.commit()
        print("Initial data created successfully!")

if __name__ == "__main__":
    init_db()
    create_initial_data()
