from datetime import datetime
from .database import get_db_session, init_db
from .models import User, Brand
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def create_initial_data():
    """Create initial data for testing"""
    with get_db_session() as db:
        # Create admin user
        admin = User(
            email="<EMAIL>",
            hashed_password=pwd_context.hash("admin123"),
            full_name="System Admin",
            is_admin=True,
            is_active=True
        )
        db.add(admin)
        
        # Create sample brands
        brands = [
            Brand(
                name="Nike",
                description="Global sports apparel brand",
                industry="Sports & Fashion",
                keywords=["nike", "just do it", "nike shoes", "nike sneakers"],
                competitors=["Adidas", "Puma", "Under Armour", "New Balance"]
            ),
            Brand(
                name="Apple",
                description="Technology company",
                industry="Technology",
                keywords=["apple", "iphone", "macbook", "ipad", "apple products"],
                competitors=["Samsung", "Google", "Microsoft", "Sony"]
            )
        ]
        
        for brand in brands:
            db.add(brand)
        
        db.commit()
        print("Initial data created successfully!")

if __name__ == "__main__":
    init_db()
    create_initial_data()
