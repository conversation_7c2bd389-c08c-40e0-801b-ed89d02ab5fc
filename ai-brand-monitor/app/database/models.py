from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, Text, ForeignKey, Boolean, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, sessionmaker
from datetime import datetime
import json

Base = declarative_base()

class Brand(Base):
    __tablename__ = 'brands'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False, unique=True)
    description = Column(Text)
    industry = Column(String(50))
    keywords = Column(JSON)  # Lista słów kluczowych do monitorowania
    competitors = Column(JSON)  # Lista nazw konkurentów
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = Column(Boolean, default=True)
    
    # Relationships
    queries = relationship("Query", back_populates="brand", cascade="all, delete-orphan")
    mentions = relationship("Mention", back_populates="brand", cascade="all, delete-orphan")
    analytics = relationship("Analytics", back_populates="brand", cascade="all, delete-orphan")
    alerts = relationship("Alert", back_populates="brand", cascade="all, delete-orphan")

class Query(Base):
    __tablename__ = 'queries'
    
    id = Column(Integer, primary_key=True)
    brand_id = Column(Integer, ForeignKey('brands.id'), nullable=False)
    query_text = Column(Text, nullable=False)
    query_type = Column(String(50))  # 'product_comparison', 'brand_info', 'recommendation', etc.
    platform = Column(String(50))  # 'chatgpt', 'claude', 'perplexity'
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    brand = relationship("Brand", back_populates="queries")
    responses = relationship("AIResponse", back_populates="query", cascade="all, delete-orphan")

class AIResponse(Base):
    __tablename__ = 'ai_responses'
    
    id = Column(Integer, primary_key=True)
    query_id = Column(Integer, ForeignKey('queries.id'), nullable=False)
    platform = Column(String(50), nullable=False)
    response_text = Column(Text, nullable=False)
    response_metadata = Column(JSON)  # model version, temperature, etc.
    response_time = Column(Float)  # Response time in seconds
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    query = relationship("Query", back_populates="responses")
    mentions = relationship("Mention", back_populates="response", cascade="all, delete-orphan")

class Mention(Base):
    __tablename__ = 'mentions'
    
    id = Column(Integer, primary_key=True)
    brand_id = Column(Integer, ForeignKey('brands.id'), nullable=False)
    response_id = Column(Integer, ForeignKey('ai_responses.id'), nullable=False)
    mention_text = Column(Text)  # Exact text where brand is mentioned
    context = Column(Text)  # Surrounding context
    sentiment_score = Column(Float)  # -1 to 1
    confidence_score = Column(Float)  # 0 to 1
    position_in_response = Column(Integer)  # Where in response it appears
    is_primary_focus = Column(Boolean, default=False)  # Is brand the main topic?
    competitor_mentions = Column(JSON)  # Which competitors were also mentioned
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    brand = relationship("Brand", back_populates="mentions")
    response = relationship("AIResponse", back_populates="mentions")

class Analytics(Base):
    __tablename__ = 'analytics'
    
    id = Column(Integer, primary_key=True)
    brand_id = Column(Integer, ForeignKey('brands.id'), nullable=False)
    date = Column(DateTime, nullable=False)
    visibility_score = Column(Float)  # 0-100
    sentiment_score = Column(Float)  # -100 to 100
    authority_score = Column(Float)  # 0-100
    share_of_voice = Column(Float)  # 0-100 percentage
    total_mentions = Column(Integer)
    positive_mentions = Column(Integer)
    negative_mentions = Column(Integer)
    neutral_mentions = Column(Integer)
    competitor_comparison = Column(JSON)  # Detailed competitor metrics
    top_keywords = Column(JSON)  # Most associated keywords
    platform_breakdown = Column(JSON)  # Metrics per platform
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    brand = relationship("Brand", back_populates="analytics")

class Alert(Base):
    __tablename__ = 'alerts'

    id = Column(Integer, primary_key=True)
    brand_id = Column(Integer, ForeignKey('brands.id'), nullable=False)
    alert_type = Column(String(50))  # 'visibility_drop', 'negative_mention', 'competitor_surge'
    severity = Column(String(20))  # 'low', 'medium', 'high', 'critical'
    title = Column(String(200))
    message = Column(Text)
    data = Column(JSON)  # Additional context data
    is_read = Column(Boolean, default=False)
    is_resolved = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    resolved_at = Column(DateTime, nullable=True)

    # Relationships
    brand = relationship("Brand", back_populates="alerts")

class User(Base):
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True)
    email = Column(String(100), unique=True, nullable=False)
    hashed_password = Column(String(200), nullable=False)
    full_name = Column(String(100))
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    last_login = Column(DateTime, nullable=True)

    # Relationships
    user_brands = relationship("UserBrand", back_populates="user", cascade="all, delete-orphan")

class UserBrand(Base):
    __tablename__ = 'user_brands'

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    brand_id = Column(Integer, ForeignKey('brands.id'), nullable=False)
    role = Column(String(50), default='viewer')  # 'viewer', 'editor', 'admin'
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="user_brands")
    brand = relationship("Brand")

class ScanHistory(Base):
    __tablename__ = 'scan_history'

    id = Column(Integer, primary_key=True)
    brand_id = Column(Integer, ForeignKey('brands.id'), nullable=False)
    scan_type = Column(String(50))  # 'manual', 'scheduled', 'api'
    status = Column(String(20))  # 'pending', 'running', 'completed', 'failed'
    queries_count = Column(Integer, default=0)
    responses_count = Column(Integer, default=0)
    mentions_count = Column(Integer, default=0)
    error_message = Column(Text, nullable=True)
    started_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime, nullable=True)

    brand = relationship("Brand")
