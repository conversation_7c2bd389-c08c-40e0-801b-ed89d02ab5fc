from abc import ABC, abstractmethod
from typing import Dict, List, Optional
import time
import random
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from fake_useragent import UserAgent
import logging
from tenacity import retry, stop_after_attempt, wait_exponential

logger = logging.getLogger(__name__)

class BaseScraper(ABC):
    """Abstract base class for AI platform scrapers"""
    
    def __init__(self, platform_name: str, headless: bool = True):
        self.platform_name = platform_name
        self.headless = headless
        self.ua = UserAgent()
        self.driver = None
        
    def setup_driver(self):
        """Setup Chrome driver with anti-detection measures"""
        options = Options()
        
        if self.headless:
            options.add_argument('--headless')
        
        # Anti-detection measures
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument(f'user-agent={self.ua.random}')
        options.add_argument('--disable-gpu')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        
        # Additional privacy options
        options.add_argument('--disable-web-security')
        options.add_argument('--disable-features=VizDisplayCompositor')
        options.add_argument('--disable-logging')
        options.add_argument('--log-level=3')
        
        self.driver = webdriver.Chrome(options=options)
        
        # Execute script to remove webdriver property
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
    def close_driver(self):
        """Safely close the driver"""
        if self.driver:
            self.driver.quit()
            self.driver = None
    
    def random_delay(self, min_seconds: float = 1, max_seconds: float = 3):
        """Add random delay to mimic human behavior"""
        delay = random.uniform(min_seconds, max_seconds)
        time.sleep(delay)
    
    def human_like_typing(self, element, text: str):
        """Type text with human-like delays"""
        element.clear()
        for char in text:
            element.send_keys(char)
            time.sleep(random.uniform(0.05, 0.2))
    
    @abstractmethod
    def login(self, credentials: Dict[str, str]) -> bool:
        """Login to the platform if required"""
        pass
    
    @abstractmethod
    def send_query(self, query: str) -> Optional[str]:
        """Send query and get response"""
        pass
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def scrape_response(self, query: str, credentials: Optional[Dict] = None) -> Dict:
        """Main method to scrape response with retry logic"""
        try:
            start_time = time.time()
            
            # Setup driver if not already done
            if not self.driver:
                self.setup_driver()
            
            # Login if credentials provided and required
            if credentials and hasattr(self, 'requires_login') and self.requires_login:
                login_success = self.login(credentials)
                if not login_success:
                    raise Exception("Login failed")
            
            # Send query and get response
            response_text = self.send_query(query)
            
            if not response_text:
                raise Exception("No response received")
            
            response_time = time.time() - start_time
            
            return {
                'platform': self.platform_name,
                'query': query,
                'response': response_text,
                'response_time': response_time,
                'timestamp': datetime.utcnow().isoformat(),
                'success': True,
                'error': None
            }
            
        except Exception as e:
            logger.error(f"Error scraping {self.platform_name}: {str(e)}")
            return {
                'platform': self.platform_name,
                'query': query,
                'response': None,
                'response_time': None,
                'timestamp': datetime.utcnow().isoformat(),
                'success': False,
                'error': str(e)
            }
