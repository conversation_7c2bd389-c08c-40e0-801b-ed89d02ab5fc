from .base_scraper import BaseScraper
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from typing import Dict, Optional
import logging

logger = logging.getLogger(__name__)

class PerplexityScraper(BaseScraper):
    """Scraper for Perplexity AI responses"""
    
    def __init__(self, headless: bool = True):
        super().__init__("perplexity", headless)
        self.base_url = "https://www.perplexity.ai"
        self.requires_login = False  # Perplexity can be used without login
    
    def login(self, credentials: Dict[str, str]) -> bool:
        """Login to Perplexity (optional)"""
        # Perplexity doesn't require login for basic usage
        return True
    
    def send_query(self, query: str) -> Optional[str]:
        """Send query to Perplexity and get response"""
        try:
            # Navigate to Perplexity
            self.driver.get(self.base_url)
            self.random_delay(2, 4)
            
            # Find the search input
            search_input = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//textarea[@placeholder='Ask anything...']"))
            )
            
            # Type the query
            self.human_like_typing(search_input, query)
            self.random_delay(0.5, 1)
            
            # Submit the query
            search_input.send_keys(Keys.RETURN)
            
            # Wait for response to load
            self.random_delay(5, 8)
            
            # Wait for the response to appear
            WebDriverWait(self.driver, 30).until(
                EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'answer')]"))
            )
            
            # Get the response text
            response_element = self.driver.find_element(By.XPATH, "//div[contains(@class, 'answer')]")
            response_text = response_element.text
            
            return response_text
            
        except Exception as e:
            logger.error(f"Error sending query to Perplexity: {str(e)}")
            return None
