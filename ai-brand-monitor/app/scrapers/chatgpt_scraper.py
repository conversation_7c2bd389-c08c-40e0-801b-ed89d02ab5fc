from .base_scraper import BaseScraper
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from typing import Dict, Optional
import time
import logging

logger = logging.getLogger(__name__)

class ChatGPTScraper(BaseScraper):
    """Scraper for ChatGPT responses"""
    
    def __init__(self, headless: bool = True):
        super().__init__("chatgpt", headless)
        self.base_url = "https://chat.openai.com"
        self.requires_login = True
    
    def login(self, credentials: Dict[str, str]) -> bool:
        """Login to ChatGPT"""
        try:
            self.driver.get(self.base_url)
            self.random_delay(2, 4)
            
            # Click login button
            login_btn = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Log in')]"))
            )
            login_btn.click()
            self.random_delay()
            
            # Enter email
            email_input = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.ID, "username"))
            )
            self.human_like_typing(email_input, credentials['email'])
            
            # Click continue
            continue_btn = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            continue_btn.click()
            self.random_delay()
            
            # Enter password
            password_input = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.ID, "password"))
            )
            self.human_like_typing(password_input, credentials['password'])
            
            # Submit
            submit_btn = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            submit_btn.click()
            self.random_delay(3, 5)
            
            # Check if logged in successfully
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.XPATH, "//textarea[@data-id='root']"))
            )
            
            logger.info("Successfully logged in to ChatGPT")
            return True
            
        except Exception as e:
            logger.error(f"ChatGPT login failed: {str(e)}")
            return False
    
    def send_query(self, query: str) -> Optional[str]:
        """Send query to ChatGPT and get response"""
        try:
            # Find the input textarea
            input_box = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//textarea[@data-id='root']"))
            )
            
            # Type the query
            self.human_like_typing(input_box, query)
            self.random_delay(0.5, 1)
            
            # Send the message
            input_box.send_keys(Keys.RETURN)
            
            # Wait for response to start generating
            self.random_delay(2, 3)
            
            # Wait for response to complete (look for stop generating button to disappear)
            WebDriverWait(self.driver, 60).until_not(
                EC.presence_of_element_located((By.XPATH, "//button[contains(text(), 'Stop generating')]"))
            )
            
            # Get the last message (AI response)
            messages = self.driver.find_elements(By.XPATH, "//div[@data-message-author-role='assistant']")
            
            if messages:
                last_message = messages[-1]
                response_text = last_message.text
                return response_text
            
            return None
            
        except Exception as e:
            logger.error(f"Error sending query to ChatGPT: {str(e)}")
            return None
