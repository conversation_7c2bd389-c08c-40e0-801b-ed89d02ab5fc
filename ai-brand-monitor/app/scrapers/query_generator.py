import random
from typing import List, Dict
import json

class QueryGenerator:
    """Generate intelligent queries for brand monitoring"""
    
    def __init__(self):
        self.query_templates = {
            'product_comparison': [
                "What are the best {product} brands?",
                "Compare {brand} vs {competitor} {product}",
                "Which is better: {brand} or {competitor}?",
                "What are the pros and cons of {brand} {product}?",
                "Should I buy {brand} or {competitor} {product}?",
                "What makes {brand} different from {competitor}?",
                "Is {brand} worth the price compared to {competitor}?",
                "Which {product} brand offers the best value: {brand} or {competitor}?"
            ],
            'brand_reputation': [
                "What do people think about {brand}?",
                "Is {brand} a good company?",
                "What is {brand} known for?",
                "What are {brand}'s strengths and weaknesses?",
                "How reliable is {brand}?",
                "What is {brand}'s reputation in the {industry} industry?",
                "Why do people choose {brand}?",
                "What makes {brand} popular?"
            ],
            'product_recommendations': [
                "What are the best {product} to buy in 2024?",
                "Recommend good {product} brands",
                "What {product} should I buy?",
                "Top 5 {product} brands",
                "Best {product} for beginners",
                "Most popular {product} brands",
                "What {product} do experts recommend?",
                "Best budget {product} options"
            ],
            'industry_leadership': [
                "Who are the leaders in {industry}?",
                "What companies dominate the {industry} market?",
                "Which {industry} company is the most innovative?",
                "Who sets trends in the {industry} industry?",
                "What are the top {industry} companies?",
                "Which {industry} brand is most trusted?",
                "Who is winning in the {industry} space?",
                "What {industry} company has the best reputation?"
            ],
            'purchase_intent': [
                "Should I buy from {brand}?",
                "Is {brand} {product} worth buying?",
                "What should I know before buying {brand} {product}?",
                "Are {brand} products good quality?",
                "Is {brand} trustworthy?",
                "What are the reviews for {brand} {product}?",
                "Would you recommend {brand}?",
                "Is {brand} better than other options?"
            ],
            'problem_solving': [
                "How to choose between {brand} and {competitor}?",
                "What to consider when buying {product}?",
                "Help me decide on {product} brand",
                "I need advice on {product} purchase",
                "What factors matter when choosing {product}?",
                "How to pick the right {product} brand?",
                "What questions to ask when buying {product}?",
                "Guide to selecting {product}"
            ]
        }
        
        self.product_variations = {
            'technology': ['smartphone', 'laptop', 'tablet', 'headphones', 'smartwatch'],
            'automotive': ['car', 'SUV', 'electric vehicle', 'truck', 'motorcycle'],
            'fashion': ['shoes', 'clothing', 'accessories', 'bags', 'watches'],
            'food': ['restaurant', 'food delivery', 'snacks', 'beverages', 'coffee'],
            'finance': ['bank', 'credit card', 'investment platform', 'insurance', 'loan'],
            'travel': ['airline', 'hotel', 'booking platform', 'car rental', 'travel insurance'],
            'health': ['fitness tracker', 'supplements', 'healthcare provider', 'medical device'],
            'home': ['furniture', 'appliances', 'home security', 'cleaning products', 'tools']
        }
    
    def generate_queries(self, brand_name: str, competitors: List[str], 
                        keywords: List[str], industry: str, num_queries: int = 10) -> List[Dict]:
        """Generate diverse queries for brand monitoring"""
        queries = []
        
        # Determine product categories based on industry and keywords
        products = self._get_relevant_products(industry, keywords)
        
        # Generate queries from each category
        categories = list(self.query_templates.keys())
        queries_per_category = max(1, num_queries // len(categories))
        
        for category in categories:
            category_queries = self._generate_category_queries(
                category, brand_name, competitors, products, queries_per_category
            )
            queries.extend(category_queries)
        
        # Fill remaining slots with random queries
        while len(queries) < num_queries:
            category = random.choice(categories)
            additional_query = self._generate_category_queries(
                category, brand_name, competitors, products, 1
            )
            queries.extend(additional_query)
        
        # Shuffle and return requested number
        random.shuffle(queries)
        return queries[:num_queries]
    
    def _get_relevant_products(self, industry: str, keywords: List[str]) -> List[str]:
        """Get relevant product terms based on industry and keywords"""
        products = []
        
        # Add industry-specific products
        industry_lower = industry.lower() if industry else ""
        for category, product_list in self.product_variations.items():
            if category in industry_lower:
                products.extend(product_list)
        
        # Add products from keywords
        for keyword in keywords:
            keyword_lower = keyword.lower()
            for category, product_list in self.product_variations.items():
                for product in product_list:
                    if product in keyword_lower or keyword_lower in product:
                        products.append(product)
        
        # Default products if none found
        if not products:
            products = ['product', 'service', 'solution']
        
        return list(set(products))  # Remove duplicates
    
    def _generate_category_queries(self, category: str, brand_name: str, 
                                 competitors: List[str], products: List[str], 
                                 count: int) -> List[Dict]:
        """Generate queries for a specific category"""
        queries = []
        templates = self.query_templates[category]
        
        for _ in range(count):
            template = random.choice(templates)
            competitor = random.choice(competitors) if competitors else "other brands"
            product = random.choice(products)
            
            # Replace placeholders
            query_text = template.format(
                brand=brand_name,
                competitor=competitor,
                product=product,
                industry=brand_name  # Fallback
            )
            
            queries.append({
                'query_text': query_text,
                'query_type': category,
                'target_brand': brand_name,
                'mentioned_competitor': competitor if competitor != "other brands" else None,
                'product_focus': product
            })
        
        return queries
