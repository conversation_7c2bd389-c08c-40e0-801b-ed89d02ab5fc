import smtplib
import asyncio
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Dict, Any
from datetime import datetime
from loguru import logger

from app.config import config


class EmailSender:
    """Email notification sender for AI Brand Monitor"""
    
    def __init__(self):
        self.smtp_host = config.SMTP_HOST
        self.smtp_port = config.SMTP_PORT
        self.smtp_user = config.SMTP_USER
        self.smtp_password = config.SMTP_PASSWORD
        
    async def send_weekly_report(
        self,
        recipient_email: str,
        brand_name: str,
        report_data: Dict[str, Any]
    ) -> bool:
        """Send weekly brand monitoring report"""
        
        if not self._is_configured():
            logger.warning("Email not configured, skipping weekly report")
            return False
        
        try:
            subject = f"Weekly Brand Report - {brand_name}"
            
            # Generate HTML content
            html_content = self._generate_weekly_report_html(brand_name, report_data)
            
            # Generate plain text content
            text_content = self._generate_weekly_report_text(brand_name, report_data)
            
            success = await self._send_email(
                recipient_email,
                subject,
                text_content,
                html_content
            )
            
            if success:
                logger.info(f"Weekly report sent to {recipient_email} for brand {brand_name}")
            else:
                logger.error(f"Failed to send weekly report to {recipient_email}")
                
            return success
            
        except Exception as e:
            logger.error(f"Error sending weekly report: {str(e)}")
            return False
    
    async def send_usage_alert(
        self,
        recipient_email: str,
        alert_type: str,
        utilization_percent: float,
        monthly_budget: float,
        monthly_spent: float
    ) -> bool:
        """Send API usage alert"""
        
        if not self._is_configured():
            logger.warning("Email not configured, skipping usage alert")
            return False
        
        try:
            if alert_type == "budget_exceeded":
                subject = "⚠️ API Budget Exceeded - AI Brand Monitor"
                message = f"""
Your monthly API budget has been exceeded.

Budget: ${monthly_budget:.2f}
Spent: ${monthly_spent:.2f}
Utilization: {utilization_percent:.1f}%

Please review your usage or increase your budget to continue monitoring.
"""
            else:
                subject = "⚠️ API Budget Alert - AI Brand Monitor"
                message = f"""
You are approaching your monthly API budget limit.

Budget: ${monthly_budget:.2f}
Spent: ${monthly_spent:.2f}
Utilization: {utilization_percent:.1f}%

Consider reviewing your usage or increasing your budget.
"""
            
            success = await self._send_email(
                recipient_email,
                subject,
                message
            )
            
            if success:
                logger.info(f"Usage alert sent to {recipient_email}")
            else:
                logger.error(f"Failed to send usage alert to {recipient_email}")
                
            return success
            
        except Exception as e:
            logger.error(f"Error sending usage alert: {str(e)}")
            return False
    
    async def send_monitoring_alert(
        self,
        recipient_email: str,
        brand_name: str,
        alert_type: str,
        alert_message: str
    ) -> bool:
        """Send monitoring alert (visibility drop, negative sentiment, etc.)"""
        
        if not self._is_configured():
            logger.warning("Email not configured, skipping monitoring alert")
            return False
        
        try:
            subject_map = {
                "visibility_drop": f"📉 Visibility Drop Alert - {brand_name}",
                "negative_sentiment": f"😟 Negative Sentiment Alert - {brand_name}",
                "monitoring_failure": f"🚨 Monitoring Failure - {brand_name}"
            }
            
            subject = subject_map.get(alert_type, f"🔔 Brand Alert - {brand_name}")
            
            message = f"""
Brand Monitoring Alert for {brand_name}

Alert Type: {alert_type.replace('_', ' ').title()}
Message: {alert_message}

Time: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}

Please review your brand monitoring dashboard for more details.
"""
            
            success = await self._send_email(
                recipient_email,
                subject,
                message
            )
            
            if success:
                logger.info(f"Monitoring alert sent to {recipient_email} for brand {brand_name}")
            else:
                logger.error(f"Failed to send monitoring alert to {recipient_email}")
                
            return success
            
        except Exception as e:
            logger.error(f"Error sending monitoring alert: {str(e)}")
            return False
    
    def _is_configured(self) -> bool:
        """Check if email is properly configured"""
        return all([
            self.smtp_host,
            self.smtp_port,
            self.smtp_user,
            self.smtp_password
        ])
    
    async def _send_email(
        self,
        recipient: str,
        subject: str,
        text_content: str,
        html_content: str = None
    ) -> bool:
        """Send email using SMTP"""
        
        try:
            # Create message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = self.smtp_user
            msg['To'] = recipient
            
            # Add text part
            text_part = MIMEText(text_content, 'plain')
            msg.attach(text_part)
            
            # Add HTML part if provided
            if html_content:
                html_part = MIMEText(html_content, 'html')
                msg.attach(html_part)
            
            # Send email in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            success = await loop.run_in_executor(
                None,
                self._send_smtp,
                msg,
                recipient
            )
            
            return success
            
        except Exception as e:
            logger.error(f"Error in _send_email: {str(e)}")
            return False
    
    def _send_smtp(self, msg: MIMEMultipart, recipient: str) -> bool:
        """Send email via SMTP (blocking operation)"""
        
        try:
            # Create SMTP connection
            server = smtplib.SMTP(self.smtp_host, self.smtp_port)
            server.starttls()
            server.login(self.smtp_user, self.smtp_password)
            
            # Send email
            server.send_message(msg, to_addrs=[recipient])
            server.quit()
            
            return True
            
        except Exception as e:
            logger.error(f"SMTP error: {str(e)}")
            return False
    
    def _generate_weekly_report_html(self, brand_name: str, report_data: Dict) -> str:
        """Generate HTML content for weekly report"""
        
        if "error" in report_data:
            return f"""
            <html>
            <body>
                <h2>Weekly Report - {brand_name}</h2>
                <p>No data available for the past week.</p>
            </body>
            </html>
            """
        
        metrics = report_data.get("metrics", {})
        daily_scores = report_data.get("daily_scores", [])
        
        html = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .metrics {{ display: flex; justify-content: space-around; margin: 20px 0; }}
                .metric {{ text-align: center; padding: 10px; }}
                .metric-value {{ font-size: 24px; font-weight: bold; color: #2c3e50; }}
                .metric-label {{ color: #7f8c8d; }}
                table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                .trend-improving {{ color: #27ae60; }}
                .trend-declining {{ color: #e74c3c; }}
                .trend-stable {{ color: #f39c12; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Weekly Brand Report</h1>
                <h2>{brand_name}</h2>
                <p>Report Period: {report_data.get('period', {}).get('start', 'N/A')} to {report_data.get('period', {}).get('end', 'N/A')}</p>
            </div>
            
            <div class="metrics">
                <div class="metric">
                    <div class="metric-value">{metrics.get('avg_visibility', 0)}</div>
                    <div class="metric-label">Avg Visibility</div>
                </div>
                <div class="metric">
                    <div class="metric-value">{metrics.get('avg_sentiment', 0)}</div>
                    <div class="metric-label">Avg Sentiment</div>
                </div>
                <div class="metric">
                    <div class="metric-value">{metrics.get('total_mentions', 0)}</div>
                    <div class="metric-label">Total Mentions</div>
                </div>
                <div class="metric">
                    <div class="metric-value trend-{metrics.get('trend', 'stable')}">{metrics.get('trend', 'stable').title()}</div>
                    <div class="metric-label">Trend</div>
                </div>
            </div>
            
            <h3>Daily Breakdown</h3>
            <table>
                <tr>
                    <th>Date</th>
                    <th>Visibility Score</th>
                    <th>Sentiment Score</th>
                    <th>Mentions</th>
                </tr>
        """
        
        for day in daily_scores:
            html += f"""
                <tr>
                    <td>{day.get('date', 'N/A')}</td>
                    <td>{day.get('visibility', 0)}</td>
                    <td>{day.get('sentiment', 0)}</td>
                    <td>{day.get('mentions', 0)}</td>
                </tr>
            """
        
        html += """
            </table>
            
            <p style="margin-top: 30px; color: #7f8c8d; font-size: 12px;">
                This report was generated automatically by AI Brand Monitor.
            </p>
        </body>
        </html>
        """
        
        return html
    
    def _generate_weekly_report_text(self, brand_name: str, report_data: Dict) -> str:
        """Generate plain text content for weekly report"""
        
        if "error" in report_data:
            return f"""
Weekly Report - {brand_name}

No data available for the past week.
            """
        
        metrics = report_data.get("metrics", {})
        daily_scores = report_data.get("daily_scores", [])
        
        text = f"""
Weekly Brand Report - {brand_name}

Report Period: {report_data.get('period', {}).get('start', 'N/A')} to {report_data.get('period', {}).get('end', 'N/A')}

SUMMARY METRICS:
- Average Visibility: {metrics.get('avg_visibility', 0)}
- Average Sentiment: {metrics.get('avg_sentiment', 0)}
- Total Mentions: {metrics.get('total_mentions', 0)}
- Trend: {metrics.get('trend', 'stable').title()}

DAILY BREAKDOWN:
"""
        
        for day in daily_scores:
            text += f"- {day.get('date', 'N/A')}: Visibility {day.get('visibility', 0)}, Sentiment {day.get('sentiment', 0)}, Mentions {day.get('mentions', 0)}\n"
        
        text += "\nThis report was generated automatically by AI Brand Monitor."
        
        return text
