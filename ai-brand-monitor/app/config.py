import os
from dotenv import load_dotenv
from pathlib import Path

load_dotenv()

class Config:
    # Paths
    BASE_DIR = Path(__file__).parent.parent
    DATA_DIR = BASE_DIR / "data"
    LOG_DIR = BASE_DIR / "logs"
    
    # Database
    DATABASE_URL = f"sqlite:///{DATA_DIR}/brand_monitor.db"
    
    # API Settings
    API_HOST = os.getenv("API_HOST", "0.0.0.0")
    API_PORT = int(os.getenv("API_PORT", 8000))
    SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
    ALGORITHM = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES = 30
    
    # Scraping Settings
    SCRAPING_DELAY = int(os.getenv("SCRAPING_DELAY", 5))  # seconds between requests
    MAX_RETRIES = 3
    USER_AGENTS_ROTATION = True
    
    # AI Platforms
    PLATFORMS = {
        "chatgpt": {
            "enabled": True,
            "base_url": "https://chat.openai.com",
            "requires_auth": True
        },
        "claude": {
            "enabled": True,
            "base_url": "https://claude.ai",
            "requires_auth": True
        },
        "perplexity": {
            "enabled": True,
            "base_url": "https://www.perplexity.ai",
            "requires_auth": False
        }
    }
    
    # Analysis Settings
    MIN_CONFIDENCE_SCORE = 0.7
    SENTIMENT_THRESHOLD = 0.1
    
    # OpenRouter API Configuration
    OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")
    OPENROUTER_DEFAULT_MODEL = os.getenv("OPENROUTER_DEFAULT_MODEL", "openai/gpt-3.5-turbo")
    OPENROUTER_RATE_LIMIT = int(os.getenv("OPENROUTER_RATE_LIMIT", 60))  # requests per minute
    OPENROUTER_MAX_TOKENS = int(os.getenv("OPENROUTER_MAX_TOKENS", 1000))
    OPENROUTER_TEMPERATURE = float(os.getenv("OPENROUTER_TEMPERATURE", 0.7))
    APP_URL = os.getenv("APP_URL", "http://localhost:8000")

    # User API Limits
    FREE_TIER_DAILY_LIMIT = int(os.getenv("FREE_TIER_DAILY_LIMIT", 50))
    PREMIUM_TIER_DAILY_LIMIT = int(os.getenv("PREMIUM_TIER_DAILY_LIMIT", 500))
    ENTERPRISE_TIER_DAILY_LIMIT = int(os.getenv("ENTERPRISE_TIER_DAILY_LIMIT", 2000))

    # Notifications
    SMTP_HOST = os.getenv("SMTP_HOST", "smtp.gmail.com")
    SMTP_PORT = int(os.getenv("SMTP_PORT", 587))
    SMTP_USER = os.getenv("SMTP_USER")
    SMTP_PASSWORD = os.getenv("SMTP_PASSWORD")

    # Scheduling
    SCAN_SCHEDULE = "0 9 * * *"  # Daily at 9 AM
    REPORT_SCHEDULE = "0 10 * * 1"  # Weekly on Monday at 10 AM

config = Config()
