from pydantic import BaseModel, EmailStr, Field
from typing import List, Optional, Dict
from datetime import datetime

# User schemas
class UserBase(BaseModel):
    email: EmailStr
    full_name: Optional[str] = None

class UserCreate(UserBase):
    password: str

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class User(UserBase):
    id: int
    is_active: bool
    is_admin: bool
    created_at: datetime
    
    class Config:
        from_attributes = True

class Token(BaseModel):
    access_token: str
    token_type: str

# Brand schemas
class BrandBase(BaseModel):
    name: str
    description: Optional[str] = None
    industry: Optional[str] = None
    keywords: List[str] = Field(default_factory=list)
    competitors: List[str] = Field(default_factory=list)

class BrandCreate(BrandBase):
    pass

class Brand(BrandBase):
    id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# Query schemas
class QueryCreate(BaseModel):
    brand_id: int
    query_text: str
    query_type: Optional[str] = None
    platform: Optional[str] = None

class Query(BaseModel):
    id: int
    brand_id: int
    query_text: str
    query_type: Optional[str]
    platform: Optional[str]
    created_at: datetime
    
    class Config:
        from_attributes = True

# Response schemas
class AIResponseCreate(BaseModel):
    query_id: int
    platform: str
    response_text: str
    response_metadata: Optional[Dict] = None
    response_time: Optional[float] = None

class AIResponse(BaseModel):
    id: int
    query_id: int
    platform: str
    response_text: str
    response_metadata: Optional[Dict]
    response_time: Optional[float]
    created_at: datetime
    
    class Config:
        from_attributes = True

# Mention schemas
class MentionCreate(BaseModel):
    brand_id: int
    response_id: int
    mention_text: str
    context: str
    sentiment_score: float
    confidence_score: float
    position_in_response: int
    is_primary_focus: bool = False
    competitor_mentions: Optional[List[str]] = None

class Mention(BaseModel):
    id: int
    brand_id: int
    response_id: int
    mention_text: str
    context: str
    sentiment_score: float
    confidence_score: float
    position_in_response: int
    is_primary_focus: bool
    competitor_mentions: Optional[List[str]]
    created_at: datetime
    
    class Config:
        from_attributes = True

# Analytics schemas
class AnalyticsCreate(BaseModel):
    brand_id: int
    visibility_score: float
    sentiment_score: float
    authority_score: float
    share_of_voice: float
    total_mentions: int
    positive_mentions: int
    negative_mentions: int
    neutral_mentions: int
    competitor_comparison: Optional[Dict] = None
    top_keywords: Optional[List[str]] = None
    platform_breakdown: Optional[Dict] = None

class Analytics(AnalyticsCreate):
    id: int
    date: datetime
    created_at: datetime
    
    class Config:
        from_attributes = True

# Scan schemas
class ScanRequest(BaseModel):
    brand_id: int
    platforms: List[str] = Field(default_factory=lambda: ["chatgpt", "claude", "perplexity"])
    num_queries: int = Field(default=10, ge=1, le=50)

class ScanStatus(BaseModel):
    scan_id: int
    brand_id: int
    status: str
    queries_count: int
    responses_count: int
    mentions_count: int
    started_at: datetime
    completed_at: Optional[datetime]
    error_message: Optional[str]

# Report schemas
class ReportRequest(BaseModel):
    brand_id: int
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    include_competitors: bool = True
    format: str = Field(default="json", pattern="^(json|csv|pdf)$")

class ComparisonReport(BaseModel):
    brand: str
    competitors: List[Dict[str, float]]
    visibility_comparison: Dict[str, float]
    sentiment_comparison: Dict[str, float]
    share_of_voice: Dict[str, float]
    period: Dict[str, datetime]

# Dashboard schemas
class DashboardData(BaseModel):
    brand_id: int
    brand_name: str
    current_scores: Dict[str, float]
    trends: Dict[str, str]
    recent_mentions: List[Dict]
    competitor_comparison: Dict[str, float]
    alerts: List[Dict]

class AlertCreate(BaseModel):
    brand_id: int
    alert_type: str
    severity: str
    title: str
    message: str
    data: Optional[Dict] = None

class Alert(BaseModel):
    id: int
    brand_id: int
    alert_type: str
    severity: str
    title: str
    message: str
    data: Optional[Dict]
    is_read: bool
    is_resolved: bool
    created_at: datetime
    resolved_at: Optional[datetime]
    
    class Config:
        from_attributes = True
