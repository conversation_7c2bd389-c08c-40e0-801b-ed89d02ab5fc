from pydantic import BaseModel, EmailStr, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

# User schemas
class UserBase(BaseModel):
    email: EmailStr
    full_name: Optional[str] = None

class UserCreate(UserBase):
    password: str

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class User(UserBase):
    id: int
    is_active: bool
    is_admin: bool
    created_at: datetime
    
    class Config:
        from_attributes = True

class Token(BaseModel):
    access_token: str
    token_type: str

# Brand schemas
class BrandBase(BaseModel):
    name: str
    description: Optional[str] = None
    industry: Optional[str] = None
    keywords: List[str] = Field(default_factory=list)
    competitors: List[str] = Field(default_factory=list)

class BrandCreate(BrandBase):
    pass

class Brand(BrandBase):
    id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# Query schemas
class QueryCreate(BaseModel):
    brand_id: int
    query_text: str
    query_type: Optional[str] = None
    platform: Optional[str] = None

class Query(BaseModel):
    id: int
    brand_id: int
    query_text: str
    query_type: Optional[str]
    platform: Optional[str]
    created_at: datetime
    
    class Config:
        from_attributes = True

# Response schemas
class AIResponseCreate(BaseModel):
    query_id: int
    platform: str
    response_text: str
    response_metadata: Optional[Dict] = None
    response_time: Optional[float] = None

class AIResponse(BaseModel):
    id: int
    query_id: int
    platform: str
    response_text: str
    response_metadata: Optional[Dict]
    response_time: Optional[float]
    created_at: datetime
    
    class Config:
        from_attributes = True

# Mention schemas
class MentionCreate(BaseModel):
    brand_id: int
    response_id: int
    mention_text: str
    context: str
    sentiment_score: float
    confidence_score: float
    position_in_response: int
    is_primary_focus: bool = False
    competitor_mentions: Optional[List[str]] = None

class Mention(BaseModel):
    id: int
    brand_id: int
    response_id: int
    mention_text: str
    context: str
    sentiment_score: float
    confidence_score: float
    position_in_response: int
    is_primary_focus: bool
    competitor_mentions: Optional[List[str]]
    created_at: datetime
    
    class Config:
        from_attributes = True

# Analytics schemas
class AnalyticsCreate(BaseModel):
    brand_id: int
    visibility_score: float
    sentiment_score: float
    authority_score: float
    share_of_voice: float
    total_mentions: int
    positive_mentions: int
    negative_mentions: int
    neutral_mentions: int
    competitor_comparison: Optional[Dict] = None
    top_keywords: Optional[List[str]] = None
    platform_breakdown: Optional[Dict] = None

class Analytics(AnalyticsCreate):
    id: int
    date: datetime
    created_at: datetime
    
    class Config:
        from_attributes = True

# Scan schemas
class ScanRequest(BaseModel):
    brand_id: int
    platforms: List[str] = Field(default_factory=lambda: ["chatgpt", "claude", "perplexity"])
    num_queries: int = Field(default=10, ge=1, le=50)

class ScanStatus(BaseModel):
    scan_id: int
    brand_id: int
    status: str
    queries_count: int
    responses_count: int
    mentions_count: int
    started_at: datetime
    completed_at: Optional[datetime]
    error_message: Optional[str]

# Report schemas
class ReportRequest(BaseModel):
    brand_id: int
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    include_competitors: bool = True
    format: str = Field(default="json", pattern="^(json|csv|pdf)$")

class ComparisonReport(BaseModel):
    brand: str
    competitors: List[Dict[str, float]]
    visibility_comparison: Dict[str, float]
    sentiment_comparison: Dict[str, float]
    share_of_voice: Dict[str, float]
    period: Dict[str, datetime]

# Dashboard schemas
class DashboardData(BaseModel):
    brand_id: int
    brand_name: str
    current_scores: Dict[str, float]
    trends: Dict[str, str]
    recent_mentions: List[Dict]
    competitor_comparison: Dict[str, float]
    alerts: List[Dict]

class AlertCreate(BaseModel):
    brand_id: int
    alert_type: str
    severity: str
    title: str
    message: str
    data: Optional[Dict] = None

class Alert(BaseModel):
    id: int
    brand_id: int
    alert_type: str
    severity: str
    title: str
    message: str
    data: Optional[Dict]
    is_read: bool
    is_resolved: bool
    created_at: datetime
    resolved_at: Optional[datetime]
    
    class Config:
        from_attributes = True


# OpenRouter and AI Model schemas
class AIModelBase(BaseModel):
    model_id: str
    name: str
    provider: str
    context_length: int = 4096
    cost_per_1k_prompt_tokens: float = 0.0
    cost_per_1k_completion_tokens: float = 0.0
    is_enabled: bool = True
    is_default: bool = False
    default_temperature: float = 0.7
    default_max_tokens: int = 1000
    free_tier_daily_limit: int = 10
    premium_tier_daily_limit: int = 100
    enterprise_tier_daily_limit: int = 500


class AIModelCreate(AIModelBase):
    pass


class AIModelUpdate(BaseModel):
    is_enabled: Optional[bool] = None
    is_default: Optional[bool] = None
    default_temperature: Optional[float] = None
    default_max_tokens: Optional[int] = None
    free_tier_daily_limit: Optional[int] = None
    premium_tier_daily_limit: Optional[int] = None
    enterprise_tier_daily_limit: Optional[int] = None
    cost_per_1k_prompt_tokens: Optional[float] = None
    cost_per_1k_completion_tokens: Optional[float] = None


class AIModel(AIModelBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class PageAnalysisBase(BaseModel):
    url: str
    extracted_keywords: Optional[List[str]] = []
    identified_products: Optional[List[str]] = []
    identified_competitors: Optional[List[str]] = []
    business_description: Optional[str] = None
    industry_classification: Optional[str] = None
    suggested_queries: Optional[List[str]] = []
    brand_positioning: Optional[str] = None
    target_audience: Optional[str] = None
    confidence_score: float = 0.0


class PageAnalysisCreate(PageAnalysisBase):
    brand_id: int


class PageAnalysis(PageAnalysisBase):
    id: int
    brand_id: int
    analysis_model: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class APIUsageBase(BaseModel):
    prompt_tokens: int = 0
    completion_tokens: int = 0
    total_tokens: int = 0
    cost: float = 0.0
    query_text: Optional[str] = None
    response_time: Optional[float] = None
    success: bool = True
    error_message: Optional[str] = None


class APIUsage(APIUsageBase):
    id: int
    user_id: int
    ai_model_id: int
    brand_id: Optional[int] = None
    created_at: datetime
    date: datetime

    class Config:
        from_attributes = True


class MonitoringRequest(BaseModel):
    queries: List[str]
    models: Optional[List[str]] = ["openai/gpt-3.5-turbo"]


class MonitoringResult(BaseModel):
    brand_id: int
    brand_name: str
    total_queries: int
    total_models: int
    total_mentions: int
    visibility_score: float
    sentiment_score: float
    authority_score: float
    models_coverage: Dict[str, int]
    mention_types: Dict[str, int]


class UsageSummary(BaseModel):
    period_days: int
    total_requests: int
    total_cost: float
    total_tokens: int
    success_rate: float
    today_requests: int
    today_cost: float
    average_cost_per_request: float
    model_breakdown: Dict[str, Dict[str, Any]]
    brand_breakdown: Dict[str, Dict[str, Any]]
    daily_trends: Dict[str, Dict[str, Any]]


class QuotaStatus(BaseModel):
    user_tier: str
    daily_limit: int
    used_today: int
    remaining_today: int
    daily_utilization: float
    monthly_budget: float
    monthly_spent: float
    monthly_remaining: Optional[float] = None
    monthly_utilization: float
    model_quotas: List[Dict[str, Any]]


class UserAPIUsage(BaseModel):
    usage_summary: UsageSummary
    quota_status: QuotaStatus


class OpenRouterTestResult(BaseModel):
    status: str
    message: str
    response_time: Optional[float] = None
    cost: Optional[float] = None


class ModelSyncResult(BaseModel):
    success: bool
    synced_models: Optional[int] = None
    updated_models: Optional[int] = None
    total_available: Optional[int] = None
    error: Optional[str] = None


class UsageStatistics(BaseModel):
    period_days: int
    total_requests: int
    total_cost: float
    total_tokens: int
    success_rate: float
    average_cost_per_request: float
    model_breakdown: Dict[str, Dict[str, Any]]
    daily_trends: Dict[str, Dict[str, Any]]
    tier_breakdown: Dict[str, Dict[str, Any]]
