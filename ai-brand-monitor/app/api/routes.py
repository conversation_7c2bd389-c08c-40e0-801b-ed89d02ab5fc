from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, timed<PERSON>ta
import json

from app.database.database import get_db
from app.database import models
from app.api import schemas
from app.api.auth import (
    authenticate_user, create_access_token, get_current_active_user,
    get_password_hash, get_current_admin_user
)
from app.scrapers.query_generator import QueryGenerator
from app.analyzers.nlp_analyzer import NLPAnalyzer
from app.analyzers.sentiment import SentimentAnalyzer
from app.analyzers.scoring import ScoringEngine
from app.analyzers.competitor import CompetitorAnalyzer
from app.analyzers.page_analyzer import PageAnalyzer
from app.clients.openrouter_client import OpenRouterClient
from app.clients.ai_model_monitor import AIModelMonitor
from app.admin.model_management import ModelManager
from app.utils.api_usage_tracker import APIUsageTracker
from app.config import config

router = APIRouter()

# Authentication endpoints
@router.post("/token", response_model=schemas.Token)
async def login(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    """Login endpoint"""
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=config.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.email}, expires_delta=access_token_expires
    )
    
    # Update last login
    user.last_login = datetime.utcnow()
    db.commit()
    
    return {"access_token": access_token, "token_type": "bearer"}

@router.post("/register", response_model=schemas.User)
async def register(user: schemas.UserCreate, db: Session = Depends(get_db)):
    """Register new user"""
    # Check if user exists
    db_user = db.query(models.User).filter(models.User.email == user.email).first()
    if db_user:
        raise HTTPException(status_code=400, detail="Email already registered")
    
    # Create new user
    hashed_password = get_password_hash(user.password)
    db_user = models.User(
        email=user.email,
        hashed_password=hashed_password,
        full_name=user.full_name
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    return db_user

# Brand endpoints
@router.post("/brands", response_model=schemas.Brand)
async def create_brand(
    brand: schemas.BrandCreate,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create new brand"""
    # Check if brand exists
    existing_brand = db.query(models.Brand).filter(models.Brand.name == brand.name).first()
    if existing_brand:
        raise HTTPException(status_code=400, detail="Brand already exists")
    
    # Create brand
    db_brand = models.Brand(
        name=brand.name,
        description=brand.description,
        industry=brand.industry,
        keywords=brand.keywords,
        competitors=brand.competitors
    )
    db.add(db_brand)
    db.commit()
    db.refresh(db_brand)
    
    # Create user-brand association
    user_brand = models.UserBrand(
        user_id=current_user.id,
        brand_id=db_brand.id,
        role='admin'
    )
    db.add(user_brand)
    db.commit()
    
    return db_brand

@router.get("/brands", response_model=List[schemas.Brand])
async def list_brands(
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """List user's brands"""
    # Get brands associated with user
    user_brands = db.query(models.UserBrand).filter(
        models.UserBrand.user_id == current_user.id
    ).all()
    
    brand_ids = [ub.brand_id for ub in user_brands]
    brands = db.query(models.Brand).filter(
        models.Brand.id.in_(brand_ids),
        models.Brand.is_active == True
    ).offset(skip).limit(limit).all()
    
    return brands

@router.get("/brands/{brand_id}", response_model=schemas.Brand)
async def get_brand(
    brand_id: int,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get brand details"""
    # Check user has access to brand
    user_brand = db.query(models.UserBrand).filter(
        models.UserBrand.user_id == current_user.id,
        models.UserBrand.brand_id == brand_id
    ).first()
    
    if not user_brand:
        raise HTTPException(status_code=403, detail="Access denied")
    
    brand = db.query(models.Brand).filter(models.Brand.id == brand_id).first()
    if not brand:
        raise HTTPException(status_code=404, detail="Brand not found")
    
    return brand

@router.put("/brands/{brand_id}", response_model=schemas.Brand)
async def update_brand(
    brand_id: int,
    brand_update: schemas.BrandCreate,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update brand"""
    # Check user has access to brand
    user_brand = db.query(models.UserBrand).filter(
        models.UserBrand.user_id == current_user.id,
        models.UserBrand.brand_id == brand_id
    ).first()
    
    if not user_brand or user_brand.role not in ['admin', 'editor']:
        raise HTTPException(status_code=403, detail="Access denied")
    
    brand = db.query(models.Brand).filter(models.Brand.id == brand_id).first()
    if not brand:
        raise HTTPException(status_code=404, detail="Brand not found")
    
    # Update brand
    brand.name = brand_update.name
    brand.description = brand_update.description
    brand.industry = brand_update.industry
    brand.keywords = brand_update.keywords
    brand.competitors = brand_update.competitors
    brand.updated_at = datetime.utcnow()
    
    db.commit()
    db.refresh(brand)
    
    return brand

@router.delete("/brands/{brand_id}")
async def delete_brand(
    brand_id: int,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete brand"""
    # Check user has admin access to brand
    user_brand = db.query(models.UserBrand).filter(
        models.UserBrand.user_id == current_user.id,
        models.UserBrand.brand_id == brand_id,
        models.UserBrand.role == 'admin'
    ).first()
    
    if not user_brand:
        raise HTTPException(status_code=403, detail="Access denied")
    
    brand = db.query(models.Brand).filter(models.Brand.id == brand_id).first()
    if not brand:
        raise HTTPException(status_code=404, detail="Brand not found")
    
    # Soft delete
    brand.is_active = False
    db.commit()
    
    return {"message": "Brand deleted successfully"}

# Scanning endpoints
@router.post("/brands/{brand_id}/scan", response_model=schemas.ScanStatus)
async def start_scan(
    brand_id: int,
    scan_request: schemas.ScanRequest,
    background_tasks: BackgroundTasks,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Start brand scan"""
    # Verify access
    user_brand = db.query(models.UserBrand).filter(
        models.UserBrand.user_id == current_user.id,
        models.UserBrand.brand_id == brand_id
    ).first()

    if not user_brand:
        raise HTTPException(status_code=403, detail="Access denied")

    # Get brand
    brand = db.query(models.Brand).filter(models.Brand.id == brand_id).first()
    if not brand:
        raise HTTPException(status_code=404, detail="Brand not found")

    # Create scan history entry
    scan_history = models.ScanHistory(
        brand_id=brand_id,
        scan_type='manual',
        status='pending',
        queries_count=scan_request.num_queries
    )
    db.add(scan_history)
    db.commit()
    db.refresh(scan_history)

    # Start background scan
    background_tasks.add_task(
        run_brand_scan,
        scan_history.id,
        brand,
        scan_request.platforms,
        scan_request.num_queries
    )

    return schemas.ScanStatus(
        scan_id=scan_history.id,
        brand_id=brand_id,
        status=scan_history.status,
        queries_count=scan_history.queries_count,
        responses_count=scan_history.responses_count,
        mentions_count=scan_history.mentions_count,
        started_at=scan_history.started_at,
        completed_at=scan_history.completed_at,
        error_message=scan_history.error_message
    )

@router.get("/brands/{brand_id}/scan/{scan_id}", response_model=schemas.ScanStatus)
async def get_scan_status(
    brand_id: int,
    scan_id: int,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get scan status"""
    # Verify access
    user_brand = db.query(models.UserBrand).filter(
        models.UserBrand.user_id == current_user.id,
        models.UserBrand.brand_id == brand_id
    ).first()

    if not user_brand:
        raise HTTPException(status_code=403, detail="Access denied")

    scan = db.query(models.ScanHistory).filter(
        models.ScanHistory.id == scan_id,
        models.ScanHistory.brand_id == brand_id
    ).first()

    if not scan:
        raise HTTPException(status_code=404, detail="Scan not found")

    return schemas.ScanStatus(
        scan_id=scan.id,
        brand_id=scan.brand_id,
        status=scan.status,
        queries_count=scan.queries_count,
        responses_count=scan.responses_count,
        mentions_count=scan.mentions_count,
        started_at=scan.started_at,
        completed_at=scan.completed_at,
        error_message=scan.error_message
    )

# Analytics endpoints
@router.get("/brands/{brand_id}/analytics", response_model=List[schemas.Analytics])
async def get_brand_analytics(
    brand_id: int,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get brand analytics"""
    # Verify access
    user_brand = db.query(models.UserBrand).filter(
        models.UserBrand.user_id == current_user.id,
        models.UserBrand.brand_id == brand_id
    ).first()

    if not user_brand:
        raise HTTPException(status_code=403, detail="Access denied")

    # Build query
    query = db.query(models.Analytics).filter(models.Analytics.brand_id == brand_id)

    if start_date:
        query = query.filter(models.Analytics.date >= start_date)
    if end_date:
        query = query.filter(models.Analytics.date <= end_date)

    analytics = query.order_by(models.Analytics.date.desc()).all()

    return analytics

@router.get("/brands/{brand_id}/dashboard", response_model=schemas.DashboardData)
async def get_dashboard_data(
    brand_id: int,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get dashboard data for brand"""
    # Verify access
    user_brand = db.query(models.UserBrand).filter(
        models.UserBrand.user_id == current_user.id,
        models.UserBrand.brand_id == brand_id
    ).first()

    if not user_brand:
        raise HTTPException(status_code=403, detail="Access denied")

    brand = db.query(models.Brand).filter(models.Brand.id == brand_id).first()
    if not brand:
        raise HTTPException(status_code=404, detail="Brand not found")

    # Get latest analytics
    latest_analytics = db.query(models.Analytics).filter(
        models.Analytics.brand_id == brand_id
    ).order_by(models.Analytics.date.desc()).first()

    # Get recent mentions
    recent_mentions = db.query(models.Mention).filter(
        models.Mention.brand_id == brand_id
    ).order_by(models.Mention.created_at.desc()).limit(10).all()

    # Get alerts
    alerts = db.query(models.Alert).filter(
        models.Alert.brand_id == brand_id,
        models.Alert.is_resolved == False
    ).order_by(models.Alert.created_at.desc()).limit(5).all()

    # Prepare dashboard data
    current_scores = {}
    trends = {}
    competitor_comparison = {}

    if latest_analytics:
        current_scores = {
            'visibility_score': latest_analytics.visibility_score,
            'sentiment_score': latest_analytics.sentiment_score,
            'authority_score': latest_analytics.authority_score,
            'share_of_voice': latest_analytics.share_of_voice
        }

        competitor_comparison = latest_analytics.competitor_comparison or {}

    return schemas.DashboardData(
        brand_id=brand_id,
        brand_name=brand.name,
        current_scores=current_scores,
        trends=trends,
        recent_mentions=[{
            'id': m.id,
            'text': m.mention_text,
            'sentiment': m.sentiment_score,
            'platform': 'unknown',  # Would need to join with response
            'created_at': m.created_at
        } for m in recent_mentions],
        competitor_comparison=competitor_comparison,
        alerts=[{
            'id': a.id,
            'type': a.alert_type,
            'severity': a.severity,
            'title': a.title,
            'message': a.message,
            'created_at': a.created_at
        } for a in alerts]
    )


# OpenRouter API endpoints
@router.get("/openrouter/models")
async def get_available_models(
    current_user: models.User = Depends(get_current_active_user)
):
    """Get available AI models"""
    model_manager = ModelManager()
    models_data = model_manager.get_all_models()
    return {"models": models_data}


@router.post("/openrouter/test-connection")
async def test_openrouter_connection(
    api_key: str,
    current_user: models.User = Depends(get_current_active_user)
):
    """Test OpenRouter API connection"""
    client = OpenRouterClient(api_key)
    result = await client.test_connection()
    return result


@router.post("/brands/{brand_id}/analyze-page")
async def analyze_brand_page(
    brand_id: int,
    url: str,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Analyze brand website page"""
    # Verify access
    user_brand = db.query(models.UserBrand).filter(
        models.UserBrand.user_id == current_user.id,
        models.UserBrand.brand_id == brand_id
    ).first()

    if not user_brand:
        raise HTTPException(status_code=403, detail="Access denied")

    brand = db.query(models.Brand).filter(models.Brand.id == brand_id).first()
    if not brand:
        raise HTTPException(status_code=404, detail="Brand not found")

    # Check API limits
    limits_check = APIUsageTracker.check_user_limits(
        current_user.id,
        1,  # Default model ID for page analysis
        1
    )

    if not limits_check["allowed"]:
        raise HTTPException(status_code=429, detail=limits_check["reason"])

    # Analyze page
    page_analyzer = PageAnalyzer(current_user.openrouter_api_key)
    analysis_result = await page_analyzer.analyze_website(url, brand.name)

    if "error" in analysis_result:
        raise HTTPException(status_code=400, detail=analysis_result["error"])

    # Save analysis
    page_analysis = await page_analyzer.save_analysis(brand_id, url, analysis_result)

    return {
        "analysis": analysis_result,
        "saved": page_analysis is not None
    }


@router.post("/brands/{brand_id}/monitor-ai")
async def start_ai_monitoring(
    brand_id: int,
    request_data: dict,
    background_tasks: BackgroundTasks,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Start AI model monitoring for brand"""
    # Verify access
    user_brand = db.query(models.UserBrand).filter(
        models.UserBrand.user_id == current_user.id,
        models.UserBrand.brand_id == brand_id
    ).first()

    if not user_brand:
        raise HTTPException(status_code=403, detail="Access denied")

    brand = db.query(models.Brand).filter(models.Brand.id == brand_id).first()
    if not brand:
        raise HTTPException(status_code=404, detail="Brand not found")

    queries = request_data.get("queries", [])
    models_to_use = request_data.get("models", ["openai/gpt-3.5-turbo"])

    if not queries:
        raise HTTPException(status_code=400, detail="No queries provided")

    # Check API limits for all requested queries and models
    total_requests = len(queries) * len(models_to_use)

    for model_name in models_to_use:
        ai_model = db.query(models.AIModel).filter(
            models.AIModel.model_id == model_name,
            models.AIModel.is_enabled == True
        ).first()

        if not ai_model:
            raise HTTPException(status_code=400, detail=f"Model {model_name} not available")

        limits_check = APIUsageTracker.check_user_limits(
            current_user.id,
            ai_model.id,
            len(queries)
        )

        if not limits_check["allowed"]:
            raise HTTPException(status_code=429, detail=f"Limits exceeded for {model_name}: {limits_check['reason']}")

    # Start monitoring in background
    background_tasks.add_task(
        run_ai_monitoring,
        brand,
        queries,
        models_to_use,
        current_user.id
    )

    return {
        "message": "AI monitoring started",
        "brand_id": brand_id,
        "queries_count": len(queries),
        "models_count": len(models_to_use),
        "total_requests": total_requests
    }


@router.get("/users/me/api-usage")
async def get_my_api_usage(
    days: int = 30,
    current_user: models.User = Depends(get_current_active_user)
):
    """Get current user's API usage summary"""
    usage_summary = APIUsageTracker.get_user_usage_summary(current_user.id, days)
    quota_status = APIUsageTracker.get_quota_status(current_user.id)

    return {
        "usage_summary": usage_summary,
        "quota_status": quota_status
    }


@router.put("/users/me/openrouter-key")
async def update_openrouter_key(
    api_key: str,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update user's OpenRouter API key"""
    # Test the API key first
    client = OpenRouterClient(api_key)
    test_result = await client.test_connection()

    if test_result["status"] != "success":
        raise HTTPException(status_code=400, detail=f"Invalid API key: {test_result['message']}")

    # Update user's API key
    current_user.openrouter_api_key = api_key
    db.commit()

    return {"message": "OpenRouter API key updated successfully"}


# Admin endpoints
@router.get("/admin/models", dependencies=[Depends(get_current_admin_user)])
async def admin_get_models():
    """Admin: Get all AI models with statistics"""
    model_manager = ModelManager()
    return {"models": model_manager.get_all_models()}


@router.post("/admin/models/sync", dependencies=[Depends(get_current_admin_user)])
async def admin_sync_models():
    """Admin: Sync available models from OpenRouter"""
    model_manager = ModelManager()
    result = await model_manager.sync_available_models()
    return result


@router.put("/admin/models/{model_id}", dependencies=[Depends(get_current_admin_user)])
async def admin_update_model(
    model_id: int,
    config_data: dict
):
    """Admin: Update model configuration"""
    model_manager = ModelManager()
    result = model_manager.update_model_config(model_id, config_data)
    return result


@router.get("/admin/usage-stats", dependencies=[Depends(get_current_admin_user)])
async def admin_get_usage_stats(
    days: int = 30,
    model_id: Optional[int] = None
):
    """Admin: Get usage statistics"""
    model_manager = ModelManager()
    stats = model_manager.get_usage_statistics(days, model_id)
    return stats


@router.get("/admin/users-usage", dependencies=[Depends(get_current_admin_user)])
async def admin_get_users_usage(limit: int = 50):
    """Admin: Get user usage summary"""
    model_manager = ModelManager()
    users_summary = model_manager.get_user_usage_summary(limit)
    return {"users": users_summary}

# Helper functions
def get_current_admin_user(current_user: models.User = Depends(get_current_active_user)):
    """Dependency to ensure user is admin"""
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Admin access required")
    return current_user


# Background task functions
async def run_ai_monitoring(
    brand: models.Brand,
    queries: List[str],
    models_to_use: List[str],
    user_id: int
):
    """Background task for AI monitoring"""
    try:
        monitor = AIModelMonitor()
        result = await monitor.monitor_brand(
            brand=brand,
            queries=queries,
            models_to_use=models_to_use,
            user_id=user_id
        )

        logger.info(f"AI monitoring completed for brand {brand.name}: {result['total_queries']} queries, {len(result['mentions'])} mentions found")

    except Exception as e:
        logger.error(f"Error in AI monitoring for brand {brand.name}: {str(e)}")


async def run_brand_scan(scan_id: int, brand: models.Brand, platforms: List[str], num_queries: int):
    """Background task to run brand scan"""
    from app.database.database import get_db_session

    try:
        with get_db_session() as db:
            # Update scan status
            scan = db.query(models.ScanHistory).filter(models.ScanHistory.id == scan_id).first()
            scan.status = 'running'
            db.commit()

            # Generate queries
            query_generator = QueryGenerator()
            queries = query_generator.generate_queries(
                brand.name,
                brand.competitors or [],
                brand.keywords or [],
                brand.industry or "",
                num_queries
            )

            # Initialize analyzers
            nlp_analyzer = NLPAnalyzer()
            sentiment_analyzer = SentimentAnalyzer()
            scoring_engine = ScoringEngine()
            competitor_analyzer = CompetitorAnalyzer()

            total_responses = 0
            total_mentions = 0

            # Process each query
            for query_data in queries:
                # Create query record
                db_query = models.Query(
                    brand_id=brand.id,
                    query_text=query_data['query_text'],
                    query_type=query_data['query_type'],
                    platform=None  # Will be set per response
                )
                db.add(db_query)
                db.commit()
                db.refresh(db_query)

                # Process each platform
                for platform in platforms:
                    try:
                        # Here you would integrate with actual scrapers
                        # For now, we'll create mock responses
                        mock_response = f"Mock response for {query_data['query_text']} on {platform}"

                        # Create response record
                        db_response = models.AIResponse(
                            query_id=db_query.id,
                            platform=platform,
                            response_text=mock_response,
                            response_time=2.5
                        )
                        db.add(db_response)
                        db.commit()
                        db.refresh(db_response)
                        total_responses += 1

                        # Analyze response for mentions
                        mentions = nlp_analyzer.extract_brand_mentions(
                            mock_response,
                            brand.name,
                            brand.competitors or []
                        )

                        # Process each mention
                        for mention in mentions:
                            if mention['is_target_brand']:
                                # Analyze sentiment
                                sentiment_result = sentiment_analyzer.analyze_sentiment(
                                    mention['context'],
                                    brand.name
                                )

                                # Create mention record
                                db_mention = models.Mention(
                                    brand_id=brand.id,
                                    response_id=db_response.id,
                                    mention_text=mention['text'],
                                    context=mention['context'],
                                    sentiment_score=sentiment_result['brand_sentiment_score'],
                                    confidence_score=sentiment_result['brand_sentiment_confidence'],
                                    position_in_response=mention['start_pos'],
                                    is_primary_focus=True
                                )
                                db.add(db_mention)
                                total_mentions += 1

                        db.commit()

                    except Exception as e:
                        print(f"Error processing {platform}: {str(e)}")
                        continue

            # Update scan completion
            scan.status = 'completed'
            scan.responses_count = total_responses
            scan.mentions_count = total_mentions
            scan.completed_at = datetime.utcnow()
            db.commit()

    except Exception as e:
        # Update scan with error
        with get_db_session() as db:
            scan = db.query(models.ScanHistory).filter(models.ScanHistory.id == scan_id).first()
            scan.status = 'failed'
            scan.error_message = str(e)
            scan.completed_at = datetime.utcnow()
            db.commit()

# Mention endpoints
@router.get("/brands/{brand_id}/mentions", response_model=List[schemas.Mention])
async def get_brand_mentions(
    brand_id: int,
    skip: int = 0,
    limit: int = 100,
    platform: Optional[str] = None,
    sentiment: Optional[str] = None,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get brand mentions"""
    # Verify access
    user_brand = db.query(models.UserBrand).filter(
        models.UserBrand.user_id == current_user.id,
        models.UserBrand.brand_id == brand_id
    ).first()

    if not user_brand:
        raise HTTPException(status_code=403, detail="Access denied")

    # Build query
    query = db.query(models.Mention).filter(models.Mention.brand_id == brand_id)

    # Apply filters
    if sentiment:
        if sentiment == 'positive':
            query = query.filter(models.Mention.sentiment_score > 0.1)
        elif sentiment == 'negative':
            query = query.filter(models.Mention.sentiment_score < -0.1)
        elif sentiment == 'neutral':
            query = query.filter(
                models.Mention.sentiment_score >= -0.1,
                models.Mention.sentiment_score <= 0.1
            )

    mentions = query.order_by(models.Mention.created_at.desc()).offset(skip).limit(limit).all()

    return mentions

# Alert endpoints
@router.get("/brands/{brand_id}/alerts", response_model=List[schemas.Alert])
async def get_brand_alerts(
    brand_id: int,
    unread_only: bool = False,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get brand alerts"""
    # Verify access
    user_brand = db.query(models.UserBrand).filter(
        models.UserBrand.user_id == current_user.id,
        models.UserBrand.brand_id == brand_id
    ).first()

    if not user_brand:
        raise HTTPException(status_code=403, detail="Access denied")

    query = db.query(models.Alert).filter(models.Alert.brand_id == brand_id)

    if unread_only:
        query = query.filter(models.Alert.is_read == False)

    alerts = query.order_by(models.Alert.created_at.desc()).all()

    return alerts

@router.put("/alerts/{alert_id}/read")
async def mark_alert_read(
    alert_id: int,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Mark alert as read"""
    alert = db.query(models.Alert).filter(models.Alert.id == alert_id).first()
    if not alert:
        raise HTTPException(status_code=404, detail="Alert not found")

    # Verify user has access to the brand
    user_brand = db.query(models.UserBrand).filter(
        models.UserBrand.user_id == current_user.id,
        models.UserBrand.brand_id == alert.brand_id
    ).first()

    if not user_brand:
        raise HTTPException(status_code=403, detail="Access denied")

    alert.is_read = True
    db.commit()

    return {"message": "Alert marked as read"}
