from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn
import os
from pathlib import Path

from app.api.routes import router
from app.database.database import init_db
from app.scheduler.ai_scheduler import ai_scheduler
from app.config import config

# Create FastAPI app
app = FastAPI(
    title="AI Brand Monitor",
    description="Monitor and optimize brand visibility in AI chatbots",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
static_path = Path(__file__).parent.parent / "static"
if static_path.exists():
    app.mount("/static", StaticFiles(directory=str(static_path)), name="static")

# Include API routes
app.include_router(router, prefix="/api/v1")

@app.on_event("startup")
async def startup_event():
    """Initialize database and scheduler on startup"""
    # Create data directory if it doesn't exist
    config.DATA_DIR.mkdir(exist_ok=True)
    config.LOG_DIR.mkdir(exist_ok=True)

    # Initialize database
    init_db()

    # Start AI scheduler
    ai_scheduler.start()

    print("AI Brand Monitor started successfully!")


@app.on_event("shutdown")
async def shutdown_event():
    """Clean shutdown"""
    ai_scheduler.stop()
    print("AI Brand Monitor stopped")

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "AI Brand Monitor API",
        "version": "1.0.0",
        "docs": "/docs",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": "2024-01-01T00:00:00Z"}

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=config.API_HOST,
        port=config.API_PORT,
        reload=True
    )
