import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import requests
import json
import asyncio
from typing import Dict, List

# Page config
st.set_page_config(
    page_title="AI Brand Monitor Dashboard",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .alert-high {
        background-color: #ffebee;
        border-left: 4px solid #f44336;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .alert-medium {
        background-color: #fff3e0;
        border-left: 4px solid #ff9800;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .alert-low {
        background-color: #e8f5e8;
        border-left: 4px solid #4caf50;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'authenticated' not in st.session_state:
    st.session_state.authenticated = False
if 'access_token' not in st.session_state:
    st.session_state.access_token = None
if 'selected_brand' not in st.session_state:
    st.session_state.selected_brand = None

# API base URL
API_BASE_URL = "http://localhost:8000/api/v1"

def authenticate(email: str, password: str) -> bool:
    """Authenticate user with API"""
    try:
        response = requests.post(
            f"{API_BASE_URL}/token",
            data={"username": email, "password": password}
        )
        if response.status_code == 200:
            token_data = response.json()
            st.session_state.access_token = token_data["access_token"]
            st.session_state.authenticated = True
            return True
    except Exception as e:
        st.error(f"Authentication failed: {str(e)}")
    return False

def get_headers():
    """Get API headers with authentication"""
    return {
        "Authorization": f"Bearer {st.session_state.access_token}",
        "Content-Type": "application/json"
    }

def get_brands():
    """Get user's brands"""
    try:
        response = requests.get(f"{API_BASE_URL}/brands", headers=get_headers())
        if response.status_code == 200:
            return response.json()
    except Exception as e:
        st.error(f"Failed to fetch brands: {str(e)}")
    return []

def get_dashboard_data(brand_id: int):
    """Get dashboard data for brand"""
    try:
        response = requests.get(
            f"{API_BASE_URL}/brands/{brand_id}/dashboard", 
            headers=get_headers()
        )
        if response.status_code == 200:
            return response.json()
    except Exception as e:
        st.error(f"Failed to fetch dashboard data: {str(e)}")
    return None

def login_page():
    """Display login page"""
    st.title("🔐 AI Brand Monitor Login")
    
    with st.form("login_form"):
        email = st.text_input("Email")
        password = st.text_input("Password", type="password")
        submit = st.form_submit_button("Login")
        
        if submit:
            if authenticate(email, password):
                st.success("Login successful!")
                st.rerun()
            else:
                st.error("Invalid credentials")

def main_dashboard():
    """Display main dashboard"""
    st.title("📊 AI Brand Monitor Dashboard")
    
    # Sidebar
    with st.sidebar:
        st.header("Navigation")
        
        # Brand selection
        brands = get_brands()
        if brands:
            brand_names = [brand["name"] for brand in brands]
            selected_brand_name = st.selectbox("Select Brand", brand_names)
            
            # Find selected brand
            selected_brand = next(
                (brand for brand in brands if brand["name"] == selected_brand_name), 
                None
            )
            
            if selected_brand:
                st.session_state.selected_brand = selected_brand
        
        # Navigation menu
        page = st.radio(
            "Go to",
            ["Overview", "Analytics", "Mentions", "Competitors", "Alerts", "Settings"]
        )
        
        # Logout button
        if st.button("Logout"):
            st.session_state.authenticated = False
            st.session_state.access_token = None
            st.session_state.selected_brand = None
            st.rerun()
    
    # Main content
    if not st.session_state.selected_brand:
        st.warning("Please select a brand from the sidebar")
        return
    
    brand = st.session_state.selected_brand
    dashboard_data = get_dashboard_data(brand["id"])
    
    if not dashboard_data:
        st.error("Failed to load dashboard data")
        return
    
    if page == "Overview":
        show_overview(dashboard_data)
    elif page == "Analytics":
        show_analytics(brand["id"])
    elif page == "Mentions":
        show_mentions(brand["id"])
    elif page == "Competitors":
        show_competitors(brand["id"])
    elif page == "Alerts":
        show_alerts(brand["id"])
    elif page == "Settings":
        show_settings(brand)

def show_overview(dashboard_data):
    """Show overview page"""
    st.header(f"📈 {dashboard_data['brand_name']} Overview")
    
    # Key metrics
    col1, col2, col3, col4 = st.columns(4)
    
    scores = dashboard_data.get('current_scores', {})
    
    with col1:
        visibility = scores.get('visibility_score', 0)
        st.metric(
            "Visibility Score",
            f"{visibility:.1f}",
            delta=f"+{visibility-75:.1f}" if visibility > 75 else f"{visibility-75:.1f}"
        )
    
    with col2:
        sentiment = scores.get('sentiment_score', 0)
        st.metric(
            "Sentiment Score",
            f"{sentiment:.1f}",
            delta=f"+{sentiment-60:.1f}" if sentiment > 60 else f"{sentiment-60:.1f}"
        )
    
    with col3:
        authority = scores.get('authority_score', 0)
        st.metric(
            "Authority Score",
            f"{authority:.1f}",
            delta=f"+{authority-70:.1f}" if authority > 70 else f"{authority-70:.1f}"
        )
    
    with col4:
        share = scores.get('share_of_voice', 0)
        st.metric(
            "Share of Voice",
            f"{share:.1f}%",
            delta=f"+{share-25:.1f}%" if share > 25 else f"{share-25:.1f}%"
        )
    
    # Recent mentions
    st.subheader("📝 Recent Mentions")
    mentions = dashboard_data.get('recent_mentions', [])
    
    if mentions:
        for mention in mentions[:5]:
            sentiment_color = "🟢" if mention['sentiment'] > 0.1 else "🔴" if mention['sentiment'] < -0.1 else "🟡"
            st.write(f"{sentiment_color} {mention['text'][:100]}...")
    else:
        st.info("No recent mentions found")
    
    # Alerts
    st.subheader("🚨 Active Alerts")
    alerts = dashboard_data.get('alerts', [])
    
    if alerts:
        for alert in alerts:
            severity_class = f"alert-{alert['severity']}"
            st.markdown(f"""
            <div class="{severity_class}">
                <strong>{alert['title']}</strong><br>
                {alert['message']}
            </div>
            """, unsafe_allow_html=True)
    else:
        st.success("No active alerts")

def show_analytics(brand_id):
    """Show analytics page"""
    st.header("📊 Analytics")
    
    # Date range selector
    col1, col2 = st.columns(2)
    with col1:
        start_date = st.date_input("Start Date", datetime.now() - timedelta(days=30))
    with col2:
        end_date = st.date_input("End Date", datetime.now())
    
    # Mock analytics data for demonstration
    dates = pd.date_range(start=start_date, end=end_date, freq='D')
    mock_data = pd.DataFrame({
        'date': dates,
        'visibility_score': [75 + i*0.5 + (i%7)*2 for i in range(len(dates))],
        'sentiment_score': [60 + i*0.3 + (i%5)*3 for i in range(len(dates))],
        'authority_score': [70 + i*0.4 + (i%6)*1.5 for i in range(len(dates))],
        'mentions': [10 + i%8 + (i%3)*2 for i in range(len(dates))]
    })
    
    # Score trends
    fig = go.Figure()
    fig.add_trace(go.Scatter(x=mock_data['date'], y=mock_data['visibility_score'], name='Visibility'))
    fig.add_trace(go.Scatter(x=mock_data['date'], y=mock_data['sentiment_score'], name='Sentiment'))
    fig.add_trace(go.Scatter(x=mock_data['date'], y=mock_data['authority_score'], name='Authority'))
    
    fig.update_layout(title="Score Trends", xaxis_title="Date", yaxis_title="Score")
    st.plotly_chart(fig, use_container_width=True)
    
    # Mention volume
    fig2 = px.bar(mock_data, x='date', y='mentions', title="Daily Mentions")
    st.plotly_chart(fig2, use_container_width=True)

def show_mentions(brand_id):
    """Show mentions page"""
    st.header("💬 Brand Mentions")
    
    # Filters
    col1, col2, col3 = st.columns(3)
    with col1:
        platform_filter = st.selectbox("Platform", ["All", "ChatGPT", "Claude", "Perplexity"])
    with col2:
        sentiment_filter = st.selectbox("Sentiment", ["All", "Positive", "Negative", "Neutral"])
    with col3:
        date_filter = st.date_input("Date", datetime.now())
    
    # Mock mentions data
    st.info("Mentions will be displayed here based on actual API data")

def show_competitors(brand_id):
    """Show competitors page"""
    st.header("🏆 Competitive Analysis")
    st.info("Competitive analysis will be displayed here")

def show_alerts(brand_id):
    """Show alerts page"""
    st.header("🚨 Alerts & Notifications")
    st.info("Alerts will be displayed here")

def show_settings(brand):
    """Show settings page"""
    st.header("⚙️ Brand Settings")

    # OpenRouter API Configuration
    st.subheader("🔑 OpenRouter API Configuration")

    with st.form("api_settings"):
        api_key = st.text_input(
            "OpenRouter API Key",
            type="password",
            help="Enter your OpenRouter API key for AI model access"
        )

        if st.form_submit_button("Test & Save API Key"):
            if api_key:
                with st.spinner("Testing API connection..."):
                    try:
                        response = requests.post(
                            f"{API_BASE_URL}/openrouter/test-connection",
                            headers=get_headers(),
                            json={"api_key": api_key}
                        )

                        if response.status_code == 200:
                            result = response.json()
                            if result["status"] == "success":
                                # Save the API key
                                save_response = requests.put(
                                    f"{API_BASE_URL}/users/me/openrouter-key",
                                    headers=get_headers(),
                                    json={"api_key": api_key}
                                )

                                if save_response.status_code == 200:
                                    st.success("✅ API key validated and saved successfully!")
                                else:
                                    st.error("❌ Failed to save API key")
                            else:
                                st.error(f"❌ API key validation failed: {result['message']}")
                        else:
                            st.error("❌ Failed to test API connection")

                    except Exception as e:
                        st.error(f"❌ Error testing API key: {str(e)}")
            else:
                st.error("Please enter an API key")

    # API Usage Statistics
    st.subheader("📊 API Usage")

    try:
        response = requests.get(
            f"{API_BASE_URL}/users/me/api-usage",
            headers=get_headers()
        )

        if response.status_code == 200:
            usage_data = response.json()
            usage_summary = usage_data["usage_summary"]
            quota_status = usage_data["quota_status"]

            # Display quota status
            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric(
                    "Today's Usage",
                    f"{quota_status['used_today']}/{quota_status['daily_limit']}",
                    delta=f"{quota_status['remaining_today']} remaining"
                )

            with col2:
                st.metric(
                    "Monthly Cost",
                    f"${quota_status['monthly_spent']:.4f}",
                    delta=f"${quota_status.get('monthly_remaining', 0):.4f} remaining" if quota_status.get('monthly_budget') else None
                )

            with col3:
                st.metric(
                    "Success Rate",
                    f"{usage_summary['success_rate']:.1f}%"
                )

            # Model usage breakdown
            if usage_summary["model_breakdown"]:
                st.subheader("Model Usage Breakdown")
                model_df = pd.DataFrame.from_dict(usage_summary["model_breakdown"], orient="index")
                model_df = model_df.reset_index().rename(columns={"index": "Model"})
                st.dataframe(model_df)

        else:
            st.error("Failed to load usage statistics")

    except Exception as e:
        st.error(f"Error loading usage data: {str(e)}")

    # Brand Settings
    st.subheader("🏷️ Brand Configuration")

    with st.form("brand_settings"):
        name = st.text_input("Brand Name", value=brand["name"])
        description = st.text_area("Description", value=brand.get("description", ""))
        industry = st.text_input("Industry", value=brand.get("industry", ""))

        keywords = st.text_area(
            "Keywords (one per line)",
            value="\n".join(brand.get("keywords", []))
        )

        competitors = st.text_area(
            "Competitors (one per line)",
            value="\n".join(brand.get("competitors", []))
        )

        if st.form_submit_button("Save Settings"):
            st.success("Settings saved successfully!")

    # Website Analysis
    st.subheader("🌐 Website Analysis")

    website_url = st.text_input(
        "Website URL",
        placeholder="https://yourbrand.com",
        help="Analyze your website to automatically extract keywords, competitors, and generate monitoring queries"
    )

    if st.button("🔍 Analyze Website"):
        if website_url:
            with st.spinner("Analyzing website..."):
                try:
                    response = requests.post(
                        f"{API_BASE_URL}/brands/{brand['id']}/analyze-page",
                        headers=get_headers(),
                        params={"url": website_url}
                    )

                    if response.status_code == 200:
                        analysis = response.json()["analysis"]
                        st.success("✅ Website analysis completed!")

                        # Display results in expandable sections
                        with st.expander("📝 Business Description"):
                            st.write(analysis.get("business_description", "N/A"))

                        with st.expander("🏷️ Extracted Keywords"):
                            keywords = analysis.get("extracted_keywords", [])
                            if keywords:
                                st.write(", ".join(keywords))
                            else:
                                st.write("No keywords found")

                        with st.expander("🏆 Identified Competitors"):
                            competitors = analysis.get("identified_competitors", [])
                            if competitors:
                                for comp in competitors:
                                    st.write(f"• {comp}")
                            else:
                                st.write("No competitors identified")

                        with st.expander("❓ Suggested Monitoring Queries"):
                            queries = analysis.get("suggested_queries", [])
                            if queries:
                                for i, query in enumerate(queries, 1):
                                    st.write(f"{i}. {query}")
                            else:
                                st.write("No queries suggested")

                    else:
                        st.error(f"❌ Analysis failed: {response.text}")

                except Exception as e:
                    st.error(f"❌ Error analyzing website: {str(e)}")
        else:
            st.error("Please enter a website URL")

# Main app logic
def main():
    if not st.session_state.authenticated:
        login_page()
    else:
        main_dashboard()

if __name__ == "__main__":
    main()
