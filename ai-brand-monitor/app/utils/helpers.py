import re
import hashlib
import secrets
from typing import List, Dict, Any
from datetime import datetime, timedelta
import json

def clean_text(text: str) -> str:
    """Clean and normalize text"""
    if not text:
        return ""
    
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text.strip())
    
    # Remove special characters but keep basic punctuation
    text = re.sub(r'[^\w\s\.\,\!\?\-\(\)]', '', text)
    
    return text

def extract_domain(url: str) -> str:
    """Extract domain from URL"""
    pattern = r'https?://(?:www\.)?([^/]+)'
    match = re.search(pattern, url)
    return match.group(1) if match else url

def generate_api_key() -> str:
    """Generate secure API key"""
    return secrets.token_urlsafe(32)

def hash_string(text: str) -> str:
    """Generate hash of string"""
    return hashlib.sha256(text.encode()).hexdigest()

def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """Truncate text to specified length"""
    if len(text) <= max_length:
        return text
    return text[:max_length - len(suffix)] + suffix

def calculate_percentage_change(old_value: float, new_value: float) -> float:
    """Calculate percentage change between two values"""
    if old_value == 0:
        return 100.0 if new_value > 0 else 0.0
    return ((new_value - old_value) / old_value) * 100

def format_number(number: float, decimal_places: int = 2) -> str:
    """Format number with specified decimal places"""
    return f"{number:.{decimal_places}f}"

def is_valid_email(email: str) -> bool:
    """Validate email format"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe file operations"""
    # Remove or replace invalid characters
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # Remove leading/trailing spaces and dots
    filename = filename.strip(' .')
    # Limit length
    return filename[:255]

def parse_date_range(date_string: str) -> tuple:
    """Parse date range string into start and end dates"""
    today = datetime.now().date()
    
    if date_string == "today":
        return today, today
    elif date_string == "yesterday":
        yesterday = today - timedelta(days=1)
        return yesterday, yesterday
    elif date_string == "last_7_days":
        start = today - timedelta(days=7)
        return start, today
    elif date_string == "last_30_days":
        start = today - timedelta(days=30)
        return start, today
    elif date_string == "last_90_days":
        start = today - timedelta(days=90)
        return start, today
    else:
        return today - timedelta(days=30), today

def chunk_list(lst: List[Any], chunk_size: int) -> List[List[Any]]:
    """Split list into chunks of specified size"""
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]

def deep_merge_dicts(dict1: Dict, dict2: Dict) -> Dict:
    """Deep merge two dictionaries"""
    result = dict1.copy()
    
    for key, value in dict2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = deep_merge_dicts(result[key], value)
        else:
            result[key] = value
    
    return result

def safe_json_loads(json_string: str, default: Any = None) -> Any:
    """Safely parse JSON string"""
    try:
        return json.loads(json_string)
    except (json.JSONDecodeError, TypeError):
        return default

def safe_json_dumps(obj: Any, default: str = "{}") -> str:
    """Safely serialize object to JSON"""
    try:
        return json.dumps(obj, default=str)
    except (TypeError, ValueError):
        return default

def normalize_score(score: float, min_val: float = 0, max_val: float = 100) -> float:
    """Normalize score to specified range"""
    return max(min_val, min(max_val, score))

def calculate_confidence_interval(values: List[float], confidence: float = 0.95) -> tuple:
    """Calculate confidence interval for a list of values"""
    if not values:
        return 0.0, 0.0
    
    import statistics
    import math
    
    n = len(values)
    mean = statistics.mean(values)
    
    if n == 1:
        return mean, mean
    
    std_dev = statistics.stdev(values)
    std_error = std_dev / math.sqrt(n)
    
    # Use t-distribution for small samples
    if n < 30:
        # Simplified t-value approximation
        t_value = 2.0  # Approximate for 95% confidence
    else:
        t_value = 1.96  # Z-value for 95% confidence
    
    margin_error = t_value * std_error
    
    return mean - margin_error, mean + margin_error

def get_sentiment_emoji(score: float) -> str:
    """Get emoji representation of sentiment score"""
    if score > 0.3:
        return "😊"
    elif score > 0.1:
        return "🙂"
    elif score > -0.1:
        return "😐"
    elif score > -0.3:
        return "🙁"
    else:
        return "😞"

def get_trend_emoji(trend: str) -> str:
    """Get emoji representation of trend"""
    trend_emojis = {
        "improving": "📈",
        "declining": "📉",
        "stable": "➡️",
        "volatile": "📊"
    }
    return trend_emojis.get(trend, "❓")

def format_duration(seconds: float) -> str:
    """Format duration in seconds to human readable format"""
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}m"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}h"
