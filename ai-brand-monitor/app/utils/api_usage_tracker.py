from typing import Dict, List, Optional, Any
from datetime import datetime, timed<PERSON>ta
from sqlalchemy.orm import Session
from sqlalchemy import func, and_
from loguru import logger

from app.database.database import get_db_session
from app.database import models
from app.config import config


class APIUsageTracker:
    """Track and manage API usage for users and models"""
    
    @staticmethod
    def check_user_limits(
        user_id: int,
        ai_model_id: int,
        requested_queries: int = 1
    ) -> Dict[str, Any]:
        """Check if user can make the requested number of API calls"""
        
        with get_db_session() as db:
            user = db.query(models.User).filter(models.User.id == user_id).first()
            if not user:
                return {
                    "allowed": False,
                    "reason": "User not found",
                    "remaining_quota": 0
                }
            
            ai_model = db.query(models.AIModel).filter(models.AIModel.id == ai_model_id).first()
            if not ai_model:
                return {
                    "allowed": False,
                    "reason": "AI model not found",
                    "remaining_quota": 0
                }
            
            # Check if model is enabled
            if not ai_model.is_enabled:
                return {
                    "allowed": False,
                    "reason": "AI model is disabled",
                    "remaining_quota": 0
                }
            
            # Get user's daily limit
            daily_limit = user.daily_api_limit
            
            # Get model-specific limits based on user tier
            tier_limits = {
                "free": ai_model.free_tier_daily_limit,
                "premium": ai_model.premium_tier_daily_limit,
                "enterprise": ai_model.enterprise_tier_daily_limit
            }
            
            model_daily_limit = tier_limits.get(user.api_usage_tier, ai_model.free_tier_daily_limit)
            
            # Use the more restrictive limit
            effective_limit = min(daily_limit, model_daily_limit)
            
            # Get today's usage
            today = datetime.utcnow().date()
            today_usage = db.query(func.count(models.APIUsage.id)).filter(
                and_(
                    models.APIUsage.user_id == user_id,
                    models.APIUsage.ai_model_id == ai_model_id,
                    func.date(models.APIUsage.created_at) == today
                )
            ).scalar() or 0
            
            remaining_quota = max(0, effective_limit - today_usage)
            
            if today_usage + requested_queries > effective_limit:
                return {
                    "allowed": False,
                    "reason": f"Daily limit exceeded. Used: {today_usage}, Limit: {effective_limit}",
                    "remaining_quota": remaining_quota,
                    "daily_limit": effective_limit,
                    "used_today": today_usage
                }
            
            # Check monthly budget if set
            if user.monthly_api_budget > 0:
                month_start = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
                monthly_cost = db.query(func.sum(models.APIUsage.cost)).filter(
                    and_(
                        models.APIUsage.user_id == user_id,
                        models.APIUsage.created_at >= month_start
                    )
                ).scalar() or 0.0
                
                if monthly_cost >= user.monthly_api_budget:
                    return {
                        "allowed": False,
                        "reason": f"Monthly budget exceeded. Used: ${monthly_cost:.4f}, Budget: ${user.monthly_api_budget:.2f}",
                        "remaining_quota": remaining_quota,
                        "monthly_budget": user.monthly_api_budget,
                        "monthly_spent": monthly_cost
                    }
            
            return {
                "allowed": True,
                "remaining_quota": remaining_quota,
                "daily_limit": effective_limit,
                "used_today": today_usage
            }
    
    @staticmethod
    def record_usage(
        user_id: int,
        ai_model_id: int,
        brand_id: Optional[int],
        query_text: str,
        prompt_tokens: int,
        completion_tokens: int,
        total_tokens: int,
        cost: float,
        response_time: float,
        success: bool,
        error_message: Optional[str] = None
    ) -> models.APIUsage:
        """Record API usage"""
        
        with get_db_session() as db:
            usage_record = models.APIUsage(
                user_id=user_id,
                ai_model_id=ai_model_id,
                brand_id=brand_id,
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens,
                total_tokens=total_tokens,
                cost=cost,
                query_text=query_text,
                response_time=response_time,
                success=success,
                error_message=error_message,
                date=datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
            )
            
            db.add(usage_record)
            db.commit()
            db.refresh(usage_record)
            
            return usage_record
    
    @staticmethod
    def get_user_usage_summary(
        user_id: int,
        days: int = 30
    ) -> Dict[str, Any]:
        """Get user's usage summary"""
        
        with get_db_session() as db:
            start_date = datetime.utcnow() - timedelta(days=days)
            
            usage_records = db.query(models.APIUsage).filter(
                and_(
                    models.APIUsage.user_id == user_id,
                    models.APIUsage.created_at >= start_date
                )
            ).all()
            
            # Calculate totals
            total_requests = len(usage_records)
            total_cost = sum(record.cost for record in usage_records)
            total_tokens = sum(record.total_tokens for record in usage_records)
            successful_requests = len([r for r in usage_records if r.success])
            
            # Get today's usage
            today = datetime.utcnow().date()
            today_records = [r for r in usage_records if r.created_at.date() == today]
            today_requests = len(today_records)
            today_cost = sum(record.cost for record in today_records)
            
            # Group by model
            model_breakdown = {}
            for record in usage_records:
                model_name = record.ai_model.name if record.ai_model else "Unknown"
                if model_name not in model_breakdown:
                    model_breakdown[model_name] = {
                        "requests": 0,
                        "cost": 0.0,
                        "tokens": 0
                    }
                
                model_breakdown[model_name]["requests"] += 1
                model_breakdown[model_name]["cost"] += record.cost
                model_breakdown[model_name]["tokens"] += record.total_tokens
            
            # Group by brand
            brand_breakdown = {}
            for record in usage_records:
                if record.brand_id:
                    brand_name = record.brand.name if record.brand else f"Brand {record.brand_id}"
                    if brand_name not in brand_breakdown:
                        brand_breakdown[brand_name] = {
                            "requests": 0,
                            "cost": 0.0,
                            "tokens": 0
                        }
                    
                    brand_breakdown[brand_name]["requests"] += 1
                    brand_breakdown[brand_name]["cost"] += record.cost
                    brand_breakdown[brand_name]["tokens"] += record.total_tokens
            
            # Daily trends
            daily_trends = {}
            for record in usage_records:
                date_key = record.created_at.date().isoformat()
                if date_key not in daily_trends:
                    daily_trends[date_key] = {
                        "requests": 0,
                        "cost": 0.0,
                        "tokens": 0
                    }
                
                daily_trends[date_key]["requests"] += 1
                daily_trends[date_key]["cost"] += record.cost
                daily_trends[date_key]["tokens"] += record.total_tokens
            
            return {
                "period_days": days,
                "total_requests": total_requests,
                "total_cost": round(total_cost, 4),
                "total_tokens": total_tokens,
                "success_rate": (successful_requests / total_requests * 100) if total_requests > 0 else 0,
                "today_requests": today_requests,
                "today_cost": round(today_cost, 4),
                "average_cost_per_request": round(total_cost / total_requests, 6) if total_requests > 0 else 0,
                "model_breakdown": model_breakdown,
                "brand_breakdown": brand_breakdown,
                "daily_trends": daily_trends
            }
    
    @staticmethod
    def get_quota_status(user_id: int) -> Dict[str, Any]:
        """Get current quota status for user"""
        
        with get_db_session() as db:
            user = db.query(models.User).filter(models.User.id == user_id).first()
            if not user:
                return {"error": "User not found"}
            
            # Get today's usage across all models
            today = datetime.utcnow().date()
            today_usage = db.query(func.count(models.APIUsage.id)).filter(
                and_(
                    models.APIUsage.user_id == user_id,
                    func.date(models.APIUsage.created_at) == today
                )
            ).scalar() or 0
            
            # Get monthly cost
            month_start = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            monthly_cost = db.query(func.sum(models.APIUsage.cost)).filter(
                and_(
                    models.APIUsage.user_id == user_id,
                    models.APIUsage.created_at >= month_start
                )
            ).scalar() or 0.0
            
            # Get enabled models and their limits
            enabled_models = db.query(models.AIModel).filter(
                models.AIModel.is_enabled == True
            ).all()
            
            model_quotas = []
            for model in enabled_models:
                tier_limits = {
                    "free": model.free_tier_daily_limit,
                    "premium": model.premium_tier_daily_limit,
                    "enterprise": model.enterprise_tier_daily_limit
                }
                
                model_limit = tier_limits.get(user.api_usage_tier, model.free_tier_daily_limit)
                
                # Get today's usage for this model
                model_usage_today = db.query(func.count(models.APIUsage.id)).filter(
                    and_(
                        models.APIUsage.user_id == user_id,
                        models.APIUsage.ai_model_id == model.id,
                        func.date(models.APIUsage.created_at) == today
                    )
                ).scalar() or 0
                
                model_quotas.append({
                    "model_id": model.id,
                    "model_name": model.name,
                    "daily_limit": model_limit,
                    "used_today": model_usage_today,
                    "remaining": max(0, model_limit - model_usage_today),
                    "utilization_percent": (model_usage_today / model_limit * 100) if model_limit > 0 else 0
                })
            
            return {
                "user_tier": user.api_usage_tier,
                "daily_limit": user.daily_api_limit,
                "used_today": today_usage,
                "remaining_today": max(0, user.daily_api_limit - today_usage),
                "daily_utilization": (today_usage / user.daily_api_limit * 100) if user.daily_api_limit > 0 else 0,
                "monthly_budget": user.monthly_api_budget,
                "monthly_spent": round(monthly_cost, 4),
                "monthly_remaining": max(0, user.monthly_api_budget - monthly_cost) if user.monthly_api_budget > 0 else None,
                "monthly_utilization": (monthly_cost / user.monthly_api_budget * 100) if user.monthly_api_budget > 0 else 0,
                "model_quotas": model_quotas
            }
    
    @staticmethod
    def cleanup_old_usage_records(days_to_keep: int = 90) -> int:
        """Clean up old usage records to save space"""
        
        with get_db_session() as db:
            cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)
            
            deleted_count = db.query(models.APIUsage).filter(
                models.APIUsage.created_at < cutoff_date
            ).delete()
            
            db.commit()
            
            logger.info(f"Cleaned up {deleted_count} old API usage records")
            return deleted_count
    
    @staticmethod
    def get_cost_alerts(threshold_percent: float = 80.0) -> List[Dict[str, Any]]:
        """Get users approaching their budget limits"""
        
        with get_db_session() as db:
            alerts = []
            
            # Get users with monthly budgets
            users_with_budgets = db.query(models.User).filter(
                models.User.monthly_api_budget > 0
            ).all()
            
            month_start = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            
            for user in users_with_budgets:
                monthly_cost = db.query(func.sum(models.APIUsage.cost)).filter(
                    and_(
                        models.APIUsage.user_id == user.id,
                        models.APIUsage.created_at >= month_start
                    )
                ).scalar() or 0.0
                
                utilization = (monthly_cost / user.monthly_api_budget * 100) if user.monthly_api_budget > 0 else 0
                
                if utilization >= threshold_percent:
                    alerts.append({
                        "user_id": user.id,
                        "email": user.email,
                        "monthly_budget": user.monthly_api_budget,
                        "monthly_spent": round(monthly_cost, 4),
                        "utilization_percent": round(utilization, 1),
                        "alert_type": "budget_threshold" if utilization < 100 else "budget_exceeded"
                    })
            
            return alerts
