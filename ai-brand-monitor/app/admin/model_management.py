from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func
from loguru import logger

from app.database.database import get_db_session
from app.database import models
from app.clients.openrouter_client import OpenRouterClient
from app.config import config


class ModelManager:
    """Admin interface for managing AI models and configurations"""
    
    def __init__(self):
        self.openrouter_client = OpenRouterClient()
    
    async def sync_available_models(self) -> Dict[str, Any]:
        """Sync available models from OpenRouter API"""
        
        try:
            # Get models from OpenRouter
            available_models = await self.openrouter_client.get_available_models()
            
            with get_db_session() as db:
                synced_count = 0
                updated_count = 0
                
                for model_data in available_models:
                    model_id = model_data.get("id", "")
                    if not model_id:
                        continue
                    
                    # Check if model exists
                    existing_model = db.query(models.AIModel).filter(
                        models.AIModel.model_id == model_id
                    ).first()
                    
                    if existing_model:
                        # Update existing model
                        existing_model.name = model_data.get("name", model_id)
                        existing_model.context_length = model_data.get("context_length", 4096)
                        existing_model.updated_at = datetime.utcnow()
                        updated_count += 1
                    else:
                        # Create new model
                        new_model = models.AIModel(
                            model_id=model_id,
                            name=model_data.get("name", model_id),
                            provider=self._extract_provider(model_id),
                            context_length=model_data.get("context_length", 4096),
                            cost_per_1k_prompt_tokens=self._get_cost_from_pricing(model_data, "prompt"),
                            cost_per_1k_completion_tokens=self._get_cost_from_pricing(model_data, "completion"),
                            is_enabled=False,  # Disabled by default
                            is_default=False
                        )
                        db.add(new_model)
                        synced_count += 1
                
                db.commit()
                
                return {
                    "success": True,
                    "synced_models": synced_count,
                    "updated_models": updated_count,
                    "total_available": len(available_models)
                }
                
        except Exception as e:
            logger.error(f"Error syncing models: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_all_models(self) -> List[Dict[str, Any]]:
        """Get all AI models with usage statistics"""
        
        with get_db_session() as db:
            models_data = []
            
            ai_models = db.query(models.AIModel).order_by(models.AIModel.name).all()
            
            for model in ai_models:
                # Get usage statistics
                total_usage = db.query(func.count(models.APIUsage.id)).filter(
                    models.APIUsage.ai_model_id == model.id
                ).scalar() or 0
                
                total_cost = db.query(func.sum(models.APIUsage.cost)).filter(
                    models.APIUsage.ai_model_id == model.id
                ).scalar() or 0.0
                
                # Get recent usage (last 7 days)
                week_ago = datetime.utcnow() - timedelta(days=7)
                recent_usage = db.query(func.count(models.APIUsage.id)).filter(
                    models.APIUsage.ai_model_id == model.id,
                    models.APIUsage.created_at >= week_ago
                ).scalar() or 0
                
                models_data.append({
                    "id": model.id,
                    "model_id": model.model_id,
                    "name": model.name,
                    "provider": model.provider,
                    "is_enabled": model.is_enabled,
                    "is_default": model.is_default,
                    "context_length": model.context_length,
                    "cost_per_1k_prompt_tokens": model.cost_per_1k_prompt_tokens,
                    "cost_per_1k_completion_tokens": model.cost_per_1k_completion_tokens,
                    "default_temperature": model.default_temperature,
                    "default_max_tokens": model.default_max_tokens,
                    "free_tier_daily_limit": model.free_tier_daily_limit,
                    "premium_tier_daily_limit": model.premium_tier_daily_limit,
                    "enterprise_tier_daily_limit": model.enterprise_tier_daily_limit,
                    "total_usage": total_usage,
                    "total_cost": round(total_cost, 4),
                    "recent_usage": recent_usage,
                    "created_at": model.created_at,
                    "updated_at": model.updated_at
                })
            
            return models_data
    
    def update_model_config(
        self,
        model_id: int,
        config_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Update model configuration"""
        
        try:
            with get_db_session() as db:
                model = db.query(models.AIModel).filter(models.AIModel.id == model_id).first()
                
                if not model:
                    return {"success": False, "error": "Model not found"}
                
                # Update allowed fields
                allowed_fields = [
                    "is_enabled", "is_default", "default_temperature", "default_max_tokens",
                    "free_tier_daily_limit", "premium_tier_daily_limit", "enterprise_tier_daily_limit",
                    "cost_per_1k_prompt_tokens", "cost_per_1k_completion_tokens"
                ]
                
                for field in allowed_fields:
                    if field in config_data:
                        setattr(model, field, config_data[field])
                
                # If setting as default, unset other defaults
                if config_data.get("is_default"):
                    db.query(models.AIModel).filter(
                        models.AIModel.id != model_id
                    ).update({"is_default": False})
                
                model.updated_at = datetime.utcnow()
                db.commit()
                
                return {"success": True, "message": "Model configuration updated"}
                
        except Exception as e:
            logger.error(f"Error updating model config: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def get_usage_statistics(
        self,
        days: int = 30,
        model_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """Get usage statistics for admin dashboard"""
        
        with get_db_session() as db:
            start_date = datetime.utcnow() - timedelta(days=days)
            
            # Base query
            query = db.query(models.APIUsage).filter(
                models.APIUsage.created_at >= start_date
            )
            
            if model_id:
                query = query.filter(models.APIUsage.ai_model_id == model_id)
            
            usage_records = query.all()
            
            # Calculate statistics
            total_requests = len(usage_records)
            total_cost = sum(record.cost for record in usage_records)
            total_tokens = sum(record.total_tokens for record in usage_records)
            successful_requests = len([r for r in usage_records if r.success])
            
            # Group by model
            model_stats = {}
            for record in usage_records:
                model_name = record.ai_model.name if record.ai_model else "Unknown"
                if model_name not in model_stats:
                    model_stats[model_name] = {
                        "requests": 0,
                        "cost": 0.0,
                        "tokens": 0,
                        "success_rate": 0.0
                    }
                
                model_stats[model_name]["requests"] += 1
                model_stats[model_name]["cost"] += record.cost
                model_stats[model_name]["tokens"] += record.total_tokens
            
            # Calculate success rates
            for model_name in model_stats:
                model_records = [r for r in usage_records if r.ai_model and r.ai_model.name == model_name]
                successful = len([r for r in model_records if r.success])
                model_stats[model_name]["success_rate"] = (successful / len(model_records)) * 100 if model_records else 0
            
            # Group by date for trends
            daily_stats = {}
            for record in usage_records:
                date_key = record.created_at.date().isoformat()
                if date_key not in daily_stats:
                    daily_stats[date_key] = {
                        "requests": 0,
                        "cost": 0.0,
                        "tokens": 0
                    }
                
                daily_stats[date_key]["requests"] += 1
                daily_stats[date_key]["cost"] += record.cost
                daily_stats[date_key]["tokens"] += record.total_tokens
            
            # Group by user tier
            tier_stats = {}
            for record in usage_records:
                tier = record.user.api_usage_tier if record.user else "unknown"
                if tier not in tier_stats:
                    tier_stats[tier] = {
                        "requests": 0,
                        "cost": 0.0,
                        "unique_users": set()
                    }
                
                tier_stats[tier]["requests"] += 1
                tier_stats[tier]["cost"] += record.cost
                if record.user:
                    tier_stats[tier]["unique_users"].add(record.user_id)
            
            # Convert sets to counts
            for tier in tier_stats:
                tier_stats[tier]["unique_users"] = len(tier_stats[tier]["unique_users"])
            
            return {
                "period_days": days,
                "total_requests": total_requests,
                "total_cost": round(total_cost, 4),
                "total_tokens": total_tokens,
                "success_rate": (successful_requests / total_requests * 100) if total_requests > 0 else 0,
                "average_cost_per_request": round(total_cost / total_requests, 6) if total_requests > 0 else 0,
                "model_breakdown": model_stats,
                "daily_trends": daily_stats,
                "tier_breakdown": tier_stats
            }
    
    def get_user_usage_summary(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get user usage summary for admin monitoring"""
        
        with get_db_session() as db:
            # Get users with their usage in the last 30 days
            thirty_days_ago = datetime.utcnow() - timedelta(days=30)
            
            users = db.query(models.User).all()
            user_summaries = []
            
            for user in users:
                usage_records = db.query(models.APIUsage).filter(
                    models.APIUsage.user_id == user.id,
                    models.APIUsage.created_at >= thirty_days_ago
                ).all()
                
                total_requests = len(usage_records)
                total_cost = sum(record.cost for record in usage_records)
                total_tokens = sum(record.total_tokens for record in usage_records)
                
                # Get today's usage
                today = datetime.utcnow().date()
                today_requests = len([
                    r for r in usage_records 
                    if r.created_at.date() == today
                ])
                
                user_summaries.append({
                    "user_id": user.id,
                    "email": user.email,
                    "full_name": user.full_name,
                    "api_usage_tier": user.api_usage_tier,
                    "daily_api_limit": user.daily_api_limit,
                    "monthly_api_budget": user.monthly_api_budget,
                    "total_requests_30d": total_requests,
                    "total_cost_30d": round(total_cost, 4),
                    "total_tokens_30d": total_tokens,
                    "today_requests": today_requests,
                    "limit_utilization": (today_requests / user.daily_api_limit * 100) if user.daily_api_limit > 0 else 0,
                    "last_login": user.last_login,
                    "created_at": user.created_at
                })
            
            # Sort by total cost descending
            user_summaries.sort(key=lambda x: x["total_cost_30d"], reverse=True)
            
            return user_summaries[:limit]
    
    def _extract_provider(self, model_id: str) -> str:
        """Extract provider name from model ID"""
        if "/" in model_id:
            return model_id.split("/")[0].title()
        return "Unknown"
    
    def _get_cost_from_pricing(self, model_data: Dict, token_type: str) -> float:
        """Extract cost from model pricing data"""
        pricing = model_data.get("pricing", {})
        if token_type == "prompt":
            return float(pricing.get("prompt", 0)) * 1000  # Convert to per 1k tokens
        elif token_type == "completion":
            return float(pricing.get("completion", 0)) * 1000  # Convert to per 1k tokens
        return 0.0
