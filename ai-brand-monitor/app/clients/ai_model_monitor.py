import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
import json
from loguru import logger

from app.clients.openrouter_client import OpenRouterClient
from app.database.database import get_db_session
from app.database import models
from app.analyzers.nlp_analyzer import NLPAnalyzer
from app.analyzers.sentiment import SentimentAnalyzer
from app.analyzers.scoring import ScoringEngine
from app.analyzers.competitor import CompetitorAnalyzer
from app.config import config


class AIModelMonitor:
    """New monitoring system using OpenRouter API instead of scrapers"""
    
    def __init__(self, api_key: Optional[str] = None):
        self.openrouter_client = OpenRouterClient(api_key)
        self.nlp_analyzer = NLPAnalyzer()
        self.sentiment_analyzer = SentimentAnalyzer()
        self.scoring_engine = ScoringEngine()
        self.competitor_analyzer = CompetitorAnalyzer()
        
    async def monitor_brand(
        self,
        brand: models.Brand,
        queries: List[str],
        models_to_use: List[str] = None,
        user_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """Monitor brand across multiple AI models"""
        
        if not models_to_use:
            models_to_use = ["openai/gpt-3.5-turbo", "anthropic/claude-3-haiku"]
        
        results = {
            "brand_id": brand.id,
            "brand_name": brand.name,
            "total_queries": len(queries),
            "total_models": len(models_to_use),
            "responses": [],
            "mentions": [],
            "analytics": {},
            "errors": []
        }
        
        with get_db_session() as db:
            for model_name in models_to_use:
                # Check if model is enabled
                ai_model = db.query(models.AIModel).filter(
                    models.AIModel.model_id == model_name,
                    models.AIModel.is_enabled == True
                ).first()
                
                if not ai_model:
                    logger.warning(f"Model {model_name} not found or disabled")
                    continue
                
                # Check user limits if user_id provided
                if user_id and not await self._check_user_limits(user_id, ai_model.id, len(queries), db):
                    logger.warning(f"User {user_id} exceeded limits for model {model_name}")
                    continue
                
                model_results = await self._monitor_brand_with_model(
                    brand, queries, model_name, ai_model, user_id, db
                )
                
                results["responses"].extend(model_results["responses"])
                results["mentions"].extend(model_results["mentions"])
                results["errors"].extend(model_results["errors"])
        
        # Calculate overall analytics
        results["analytics"] = await self._calculate_analytics(
            brand, results["mentions"], results["responses"]
        )
        
        return results
    
    async def _monitor_brand_with_model(
        self,
        brand: models.Brand,
        queries: List[str],
        model_name: str,
        ai_model: models.AIModel,
        user_id: Optional[int],
        db
    ) -> Dict[str, Any]:
        """Monitor brand with a specific AI model"""
        
        results = {
            "responses": [],
            "mentions": [],
            "errors": []
        }
        
        # Create system prompt for brand monitoring
        system_prompt = self._create_monitoring_prompt(brand)
        
        for query_text in queries:
            try:
                # Create query record
                query_record = models.Query(
                    brand_id=brand.id,
                    query_text=query_text,
                    query_type="ai_monitoring",
                    platform="openrouter"
                )
                db.add(query_record)
                db.flush()  # Get the ID
                
                # Send query to AI model
                response_data = await self.openrouter_client.send_query(
                    query=query_text,
                    model=model_name,
                    temperature=ai_model.default_temperature,
                    max_tokens=ai_model.default_max_tokens,
                    system_prompt=system_prompt
                )
                
                # Create response record
                ai_response = models.AIResponse(
                    query_id=query_record.id,
                    ai_model_id=ai_model.id,
                    platform="openrouter",
                    model_name=model_name,
                    response_text=response_data.get("response", ""),
                    response_time=response_data.get("response_time", 0),
                    prompt_tokens=response_data.get("usage", {}).get("prompt_tokens", 0),
                    completion_tokens=response_data.get("usage", {}).get("completion_tokens", 0),
                    total_tokens=response_data.get("usage", {}).get("total_tokens", 0),
                    cost=response_data.get("cost", 0.0),
                    success=response_data.get("success", False),
                    error_message=response_data.get("error")
                )
                db.add(ai_response)
                db.flush()
                
                # Track API usage
                if user_id:
                    usage_record = models.APIUsage(
                        user_id=user_id,
                        ai_model_id=ai_model.id,
                        brand_id=brand.id,
                        prompt_tokens=ai_response.prompt_tokens,
                        completion_tokens=ai_response.completion_tokens,
                        total_tokens=ai_response.total_tokens,
                        cost=ai_response.cost,
                        query_text=query_text,
                        response_time=ai_response.response_time,
                        success=ai_response.success,
                        error_message=ai_response.error_message
                    )
                    db.add(usage_record)
                
                if response_data.get("success") and response_data.get("response"):
                    # Analyze response for brand mentions
                    mentions = await self._analyze_response(
                        ai_response, brand, query_record, db
                    )
                    results["mentions"].extend(mentions)
                
                results["responses"].append({
                    "query": query_text,
                    "model": model_name,
                    "response": response_data.get("response", ""),
                    "success": response_data.get("success", False),
                    "cost": response_data.get("cost", 0.0),
                    "tokens": response_data.get("usage", {}).get("total_tokens", 0)
                })
                
                db.commit()
                
            except Exception as e:
                logger.error(f"Error processing query '{query_text}' with model {model_name}: {str(e)}")
                results["errors"].append({
                    "query": query_text,
                    "model": model_name,
                    "error": str(e)
                })
                db.rollback()
        
        return results
    
    async def _analyze_response(
        self,
        ai_response: models.AIResponse,
        brand: models.Brand,
        query: models.Query,
        db
    ) -> List[Dict]:
        """Analyze AI response for brand mentions"""
        
        mentions_data = []
        response_text = ai_response.response_text
        
        if not response_text:
            return mentions_data
        
        # Extract brand mentions
        brand_mentions = self.nlp_analyzer.extract_brand_mentions(
            response_text,
            brand.name,
            brand.competitors or []
        )
        
        for mention_data in brand_mentions:
            # Analyze sentiment
            sentiment_result = self.sentiment_analyzer.analyze_sentiment(
                mention_data["context"],
                mention_data["brand"]
            )
            
            # Analyze mention context
            context_analysis = self.nlp_analyzer.analyze_mention_context(mention_data)
            
            # Create mention record
            mention = models.Mention(
                brand_id=brand.id,
                query_id=query.id,
                response_id=ai_response.id,
                mention_text=mention_data["text"],
                context=mention_data["context"],
                start_position=mention_data["start_pos"],
                end_position=mention_data["end_pos"],
                sentiment_score=sentiment_result["brand_sentiment_score"],
                confidence_score=sentiment_result["brand_sentiment_confidence"],
                mention_type=context_analysis["mention_type"],
                is_competitor=not mention_data["is_target_brand"]
            )
            db.add(mention)
            
            mentions_data.append({
                "brand": mention_data["brand"],
                "text": mention_data["text"],
                "context": mention_data["context"],
                "sentiment": sentiment_result["brand_sentiment_score"],
                "confidence": sentiment_result["brand_sentiment_confidence"],
                "type": context_analysis["mention_type"],
                "is_competitor": not mention_data["is_target_brand"],
                "model": ai_response.model_name
            })
        
        return mentions_data
    
    def _create_monitoring_prompt(self, brand: models.Brand) -> str:
        """Create system prompt for brand monitoring"""
        
        competitors_text = ""
        if brand.competitors:
            competitors_text = f" Consider these competitors: {', '.join(brand.competitors)}."
        
        keywords_text = ""
        if brand.keywords:
            keywords_text = f" Key topics include: {', '.join(brand.keywords)}."
        
        return f"""You are an AI assistant helping with brand monitoring. When answering questions, please provide comprehensive and helpful responses about {brand.name} and related topics in the {brand.industry or 'relevant'} industry.{competitors_text}{keywords_text}

Please be informative and balanced in your responses, mentioning relevant brands and solutions when appropriate."""
    
    async def _check_user_limits(
        self,
        user_id: int,
        ai_model_id: int,
        query_count: int,
        db
    ) -> bool:
        """Check if user has enough API quota"""
        
        user = db.query(models.User).filter(models.User.id == user_id).first()
        if not user:
            return False
        
        # Get today's usage
        today = datetime.utcnow().date()
        today_usage = db.query(models.APIUsage).filter(
            models.APIUsage.user_id == user_id,
            models.APIUsage.ai_model_id == ai_model_id,
            models.APIUsage.date >= today
        ).count()
        
        # Check against user's daily limit
        if today_usage + query_count > user.daily_api_limit:
            return False
        
        return True
    
    async def _calculate_analytics(
        self,
        brand: models.Brand,
        mentions: List[Dict],
        responses: List[Dict]
    ) -> Dict[str, Any]:
        """Calculate analytics from monitoring results"""
        
        if not mentions:
            return {
                "visibility_score": 0.0,
                "sentiment_score": 0.0,
                "authority_score": 0.0,
                "total_mentions": 0,
                "positive_mentions": 0,
                "negative_mentions": 0,
                "neutral_mentions": 0,
                "models_coverage": {},
                "mention_types": {}
            }
        
        # Convert mentions to format expected by scoring engine
        mention_objects = []
        for mention in mentions:
            mention_objects.append({
                "sentiment_score": mention["sentiment"],
                "confidence_score": mention["confidence"],
                "mention_type": mention["type"],
                "platform": mention["model"],
                "is_primary_focus": not mention["is_competitor"]
            })
        
        # Calculate scores
        visibility_score = self.scoring_engine.calculate_visibility_score(
            mention_objects, len(responses)
        )
        
        sentiment_score = self.scoring_engine.calculate_sentiment_score(mention_objects)
        
        authority_score = self.scoring_engine.calculate_authority_score(
            mention_objects, responses
        )
        
        # Count mentions by sentiment
        positive_count = len([m for m in mentions if m["sentiment"] > 0.1])
        negative_count = len([m for m in mentions if m["sentiment"] < -0.1])
        neutral_count = len(mentions) - positive_count - negative_count
        
        # Count mentions by model
        models_coverage = {}
        for mention in mentions:
            model = mention["model"]
            models_coverage[model] = models_coverage.get(model, 0) + 1
        
        # Count mentions by type
        mention_types = {}
        for mention in mentions:
            mention_type = mention["type"]
            mention_types[mention_type] = mention_types.get(mention_type, 0) + 1
        
        return {
            "visibility_score": visibility_score,
            "sentiment_score": sentiment_score,
            "authority_score": authority_score,
            "total_mentions": len(mentions),
            "positive_mentions": positive_count,
            "negative_mentions": negative_count,
            "neutral_mentions": neutral_count,
            "models_coverage": models_coverage,
            "mention_types": mention_types
        }
