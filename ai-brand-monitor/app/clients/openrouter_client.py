import asyncio
import json
import time
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import httpx
from tenacity import retry, stop_after_attempt, wait_exponential
from loguru import logger
from asyncio_throttle import Throttler

from app.config import config


class OpenRouterClient:
    """Client for OpenRouter API integration"""
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or config.OPENROUTER_API_KEY
        self.base_url = "https://openrouter.ai/api/v1"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": config.APP_URL,
            "X-Title": "AI Brand Monitor"
        }
        
        # Rate limiting - 60 requests per minute by default
        self.throttler = Throttler(rate_limit=60, period=60)
        
        # Model configurations
        self.available_models = {
            "openai/gpt-4": {
                "name": "GPT-4",
                "provider": "OpenAI",
                "context_length": 8192,
                "cost_per_1k_tokens": {"prompt": 0.03, "completion": 0.06}
            },
            "openai/gpt-3.5-turbo": {
                "name": "GPT-3.5 Turbo",
                "provider": "OpenAI", 
                "context_length": 4096,
                "cost_per_1k_tokens": {"prompt": 0.0015, "completion": 0.002}
            },
            "anthropic/claude-3-opus": {
                "name": "Claude 3 Opus",
                "provider": "Anthropic",
                "context_length": 200000,
                "cost_per_1k_tokens": {"prompt": 0.015, "completion": 0.075}
            },
            "anthropic/claude-3-sonnet": {
                "name": "Claude 3 Sonnet", 
                "provider": "Anthropic",
                "context_length": 200000,
                "cost_per_1k_tokens": {"prompt": 0.003, "completion": 0.015}
            },
            "anthropic/claude-3-haiku": {
                "name": "Claude 3 Haiku",
                "provider": "Anthropic",
                "context_length": 200000,
                "cost_per_1k_tokens": {"prompt": 0.00025, "completion": 0.00125}
            },
            "meta-llama/llama-2-70b-chat": {
                "name": "Llama 2 70B",
                "provider": "Meta",
                "context_length": 4096,
                "cost_per_1k_tokens": {"prompt": 0.0007, "completion": 0.0009}
            },
            "google/palm-2-chat-bison": {
                "name": "PaLM 2 Chat",
                "provider": "Google",
                "context_length": 8000,
                "cost_per_1k_tokens": {"prompt": 0.0005, "completion": 0.0005}
            }
        }
        
    async def get_available_models(self) -> List[Dict]:
        """Get list of available models from OpenRouter"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/models",
                    headers=self.headers,
                    timeout=30.0
                )
                response.raise_for_status()
                
                models_data = response.json()
                return models_data.get("data", [])
                
        except Exception as e:
            logger.error(f"Error fetching available models: {str(e)}")
            return []
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def send_query(
        self,
        query: str,
        model: str = "openai/gpt-3.5-turbo",
        temperature: float = 0.7,
        max_tokens: int = 1000,
        system_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """Send query to AI model via OpenRouter"""
        
        async with self.throttler:
            try:
                messages = []
                
                if system_prompt:
                    messages.append({
                        "role": "system",
                        "content": system_prompt
                    })
                
                messages.append({
                    "role": "user", 
                    "content": query
                })
                
                payload = {
                    "model": model,
                    "messages": messages,
                    "temperature": temperature,
                    "max_tokens": max_tokens,
                    "stream": False
                }
                
                start_time = time.time()
                
                async with httpx.AsyncClient() as client:
                    response = await client.post(
                        f"{self.base_url}/chat/completions",
                        headers=self.headers,
                        json=payload,
                        timeout=60.0
                    )
                    
                    response.raise_for_status()
                    response_data = response.json()
                    
                response_time = time.time() - start_time
                
                # Extract response content
                content = ""
                if "choices" in response_data and len(response_data["choices"]) > 0:
                    content = response_data["choices"][0]["message"]["content"]
                
                # Calculate usage and cost
                usage = response_data.get("usage", {})
                cost = self._calculate_cost(model, usage)
                
                return {
                    "success": True,
                    "model": model,
                    "query": query,
                    "response": content,
                    "response_time": response_time,
                    "timestamp": datetime.utcnow().isoformat(),
                    "usage": usage,
                    "cost": cost,
                    "error": None
                }
                
            except httpx.HTTPStatusError as e:
                error_msg = f"HTTP {e.response.status_code}: {e.response.text}"
                logger.error(f"OpenRouter API error: {error_msg}")
                
                return {
                    "success": False,
                    "model": model,
                    "query": query,
                    "response": None,
                    "response_time": None,
                    "timestamp": datetime.utcnow().isoformat(),
                    "usage": {},
                    "cost": 0.0,
                    "error": error_msg
                }
                
            except Exception as e:
                error_msg = str(e)
                logger.error(f"Error sending query to OpenRouter: {error_msg}")
                
                return {
                    "success": False,
                    "model": model,
                    "query": query,
                    "response": None,
                    "response_time": None,
                    "timestamp": datetime.utcnow().isoformat(),
                    "usage": {},
                    "cost": 0.0,
                    "error": error_msg
                }
    
    async def send_batch_queries(
        self,
        queries: List[str],
        model: str = "openai/gpt-3.5-turbo",
        temperature: float = 0.7,
        max_tokens: int = 1000,
        system_prompt: Optional[str] = None,
        delay_between_requests: float = 1.0
    ) -> List[Dict[str, Any]]:
        """Send multiple queries with rate limiting"""
        
        results = []
        
        for i, query in enumerate(queries):
            result = await self.send_query(
                query=query,
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                system_prompt=system_prompt
            )
            results.append(result)
            
            # Add delay between requests (except for the last one)
            if i < len(queries) - 1:
                await asyncio.sleep(delay_between_requests)
        
        return results
    
    def _calculate_cost(self, model: str, usage: Dict) -> float:
        """Calculate cost based on token usage"""
        if model not in self.available_models:
            return 0.0
        
        model_config = self.available_models[model]
        cost_config = model_config.get("cost_per_1k_tokens", {})
        
        prompt_tokens = usage.get("prompt_tokens", 0)
        completion_tokens = usage.get("completion_tokens", 0)
        
        prompt_cost = (prompt_tokens / 1000) * cost_config.get("prompt", 0)
        completion_cost = (completion_tokens / 1000) * cost_config.get("completion", 0)
        
        return round(prompt_cost + completion_cost, 6)
    
    async def test_connection(self) -> Dict[str, Any]:
        """Test OpenRouter API connection"""
        try:
            test_query = "Hello, this is a test message. Please respond with 'Connection successful'."
            result = await self.send_query(
                query=test_query,
                model="openai/gpt-3.5-turbo",
                max_tokens=50
            )
            
            if result["success"]:
                return {
                    "status": "success",
                    "message": "OpenRouter API connection successful",
                    "response_time": result["response_time"],
                    "cost": result["cost"]
                }
            else:
                return {
                    "status": "error",
                    "message": f"Connection failed: {result['error']}"
                }
                
        except Exception as e:
            return {
                "status": "error",
                "message": f"Connection test failed: {str(e)}"
            }
    
    def get_model_info(self, model: str) -> Optional[Dict]:
        """Get information about a specific model"""
        return self.available_models.get(model)
    
    def get_supported_models(self) -> List[str]:
        """Get list of supported model IDs"""
        return list(self.available_models.keys())
