from typing import Dict, List, Set
import re
from collections import defaultdict

class CompetitorAnalyzer:
    """Analyze competitor mentions and comparisons"""
    
    def __init__(self):
        self.comparison_patterns = [
            r'(\w+)\s+(?:is|are)\s+better\s+than\s+(\w+)',
            r'(\w+)\s+outperforms?\s+(\w+)',
            r'(\w+)\s+beats?\s+(\w+)',
            r'prefer\s+(\w+)\s+over\s+(\w+)',
            r'(\w+)\s+vs\.?\s+(\w+)',
            r'compared\s+to\s+(\w+),\s+(\w+)',
            r'(\w+)\s+superior\s+to\s+(\w+)',
            r'choose\s+(\w+)\s+instead\s+of\s+(\w+)'
        ]
    
    def analyze_competitor_mentions(self, responses: List[Dict], brand_name: str, competitors: List[str]) -> Dict:
        """Analyze how competitors are mentioned in responses"""
        competitor_data = {comp: {
            'mention_count': 0,
            'co_mentions': 0,  # Mentioned together with brand
            'comparison_wins': 0,
            'comparison_losses': 0,
            'contexts': []
        } for comp in competitors}
        
        brand_lower = brand_name.lower()
        
        for response in responses:
            text = response.get('response', '').lower()
            
            # Check each competitor
            for competitor in competitors:
                comp_lower = competitor.lower()
                
                # Count mentions
                mentions = len(re.findall(r'\b' + re.escape(comp_lower) + r'\b', text))
                competitor_data[competitor]['mention_count'] += mentions
                
                # Check co-mentions
                if comp_lower in text and brand_lower in text:
                    competitor_data[competitor]['co_mentions'] += 1
                    
                    # Analyze comparisons
                    comparison_result = self._analyze_comparison(text, brand_name, competitor)
                    if comparison_result == 'brand_wins':
                        competitor_data[competitor]['comparison_losses'] += 1
                    elif comparison_result == 'competitor_wins':
                        competitor_data[competitor]['comparison_wins'] += 1
                
                # Extract context
                if comp_lower in text:
                    context = self._extract_mention_context(text, competitor)
                    competitor_data[competitor]['contexts'].append(context)
        
        return competitor_data
    
    def _analyze_comparison(self, text: str, brand: str, competitor: str) -> str:
        """Determine winner in a comparison"""
        text_lower = text.lower()
        brand_lower = brand.lower()
        comp_lower = competitor.lower()
        
        # Positive indicators for brand
        brand_positive = [
            f"{brand_lower} is better",
            f"{brand_lower} outperforms",
            f"prefer {brand_lower}",
            f"{brand_lower} beats",
            f"choose {brand_lower}",
            f"{brand_lower} superior"
        ]
        
        # Positive indicators for competitor
        comp_positive = [
            f"{comp_lower} is better",
            f"{comp_lower} outperforms",
            f"prefer {comp_lower}",
            f"{comp_lower} beats",
            f"choose {comp_lower}",
            f"{comp_lower} superior"
        ]
        
        brand_score = sum(1 for phrase in brand_positive if phrase in text_lower)
        comp_score = sum(1 for phrase in comp_positive if phrase in text_lower)
        
        if brand_score > comp_score:
            return 'brand_wins'
        elif comp_score > brand_score:
            return 'competitor_wins'
        else:
            return 'neutral'
    
    def _extract_mention_context(self, text: str, competitor: str, context_size: int = 100) -> str:
        """Extract context around competitor mention"""
        comp_lower = competitor.lower()
        text_lower = text.lower()
        
        # Find first occurrence
        pos = text_lower.find(comp_lower)
        if pos == -1:
            return ""
        
        # Extract context
        start = max(0, pos - context_size)
        end = min(len(text), pos + len(competitor) + context_size)
        
        return text[start:end].strip()
    
    def calculate_competitive_landscape(self, competitor_data: Dict) -> Dict:
        """Calculate overall competitive landscape metrics"""
        total_mentions = sum(data['mention_count'] for data in competitor_data.values())
        
        landscape = {
            'total_competitor_mentions': total_mentions,
            'top_competitors': [],
            'competitive_intensity': 0,
            'market_concentration': 0
        }
        
        if total_mentions == 0:
            return landscape
        
        # Calculate market share for each competitor
        market_shares = []
        for comp, data in competitor_data.items():
            share = data['mention_count'] / total_mentions
            market_shares.append({
                'competitor': comp,
                'share': share,
                'mention_count': data['mention_count']
            })
        
        # Sort by share
        market_shares.sort(key=lambda x: x['share'], reverse=True)
        landscape['top_competitors'] = market_shares[:3]
        
        # Calculate competitive intensity (how often competitors are mentioned together)
        co_mention_rate = sum(data['co_mentions'] for data in competitor_data.values()) / len(competitor_data)
        landscape['competitive_intensity'] = min(co_mention_rate / 5, 1)  # Normalize to 0-1
        
        # Calculate market concentration (Herfindahl index)
        hhi = sum(share['share'] ** 2 for share in market_shares)
        landscape['market_concentration'] = hhi
        
        return landscape
    
    def identify_competitive_advantages(self, responses: List[Dict], brand_name: str) -> List[str]:
        """Identify mentioned competitive advantages"""
        advantages = []
        advantage_keywords = {
            'price': ['cheaper', 'affordable', 'value', 'cost-effective', 'budget'],
            'quality': ['quality', 'durable', 'reliable', 'well-made', 'premium'],
            'innovation': ['innovative', 'cutting-edge', 'advanced', 'modern', 'latest'],
            'service': ['service', 'support', 'customer care', 'responsive', 'helpful'],
            'features': ['features', 'functionality', 'capabilities', 'options', 'versatile'],
            'performance': ['performance', 'fast', 'efficient', 'powerful', 'effective'],
            'design': ['design', 'aesthetic', 'beautiful', 'stylish', 'elegant'],
            'reputation': ['trusted', 'reputable', 'established', 'popular', 'recommended']
        }
        
        brand_lower = brand_name.lower()
        
        for response in responses:
            text = response.get('response', '')
            
            # Find sentences mentioning the brand
            sentences = text.split('.')
            for sentence in sentences:
                if brand_lower in sentence.lower():
                    # Check for advantage keywords
                    for category, keywords in advantage_keywords.items():
                        if any(keyword in sentence.lower() for keyword in keywords):
                            advantages.append(category)
        
        # Return unique advantages
        return list(set(advantages))
    
    def analyze_competitive_positioning(self, brand_name: str, competitor_data: Dict, 
                                     brand_mentions: int) -> Dict:
        """Analyze brand's competitive positioning"""
        
        # Calculate brand's market share
        total_mentions = brand_mentions + sum(data['mention_count'] for data in competitor_data.values())
        brand_share = brand_mentions / total_mentions if total_mentions > 0 else 0
        
        # Find top competitors
        top_competitors = sorted(
            competitor_data.items(), 
            key=lambda x: x[1]['mention_count'], 
            reverse=True
        )[:3]
        
        # Calculate competitive metrics
        positioning = {
            'market_position': self._determine_market_position(brand_share),
            'share_of_voice': brand_share * 100,
            'top_competitors': [
                {
                    'name': comp,
                    'mention_count': data['mention_count'],
                    'share': data['mention_count'] / total_mentions * 100 if total_mentions > 0 else 0,
                    'comparison_record': {
                        'wins_against_brand': data['comparison_wins'],
                        'losses_against_brand': data['comparison_losses']
                    }
                }
                for comp, data in top_competitors
            ],
            'competitive_threats': self._identify_threats(competitor_data, brand_mentions),
            'competitive_opportunities': self._identify_opportunities(competitor_data, brand_mentions)
        }
        
        return positioning
    
    def _determine_market_position(self, share: float) -> str:
        """Determine market position based on share of voice"""
        if share >= 0.4:
            return 'market_leader'
        elif share >= 0.25:
            return 'strong_player'
        elif share >= 0.15:
            return 'competitive_player'
        elif share >= 0.05:
            return 'niche_player'
        else:
            return 'emerging_player'
    
    def _identify_threats(self, competitor_data: Dict, brand_mentions: int) -> List[Dict]:
        """Identify competitive threats"""
        threats = []
        
        for competitor, data in competitor_data.items():
            # High mention count relative to brand
            if data['mention_count'] > brand_mentions * 0.8:
                threats.append({
                    'competitor': competitor,
                    'threat_type': 'high_visibility',
                    'severity': 'high' if data['mention_count'] > brand_mentions else 'medium',
                    'details': f"High mention frequency: {data['mention_count']} vs brand's {brand_mentions}"
                })
            
            # Winning comparisons
            if data['comparison_wins'] > data['comparison_losses']:
                threats.append({
                    'competitor': competitor,
                    'threat_type': 'comparison_advantage',
                    'severity': 'medium',
                    'details': f"Wins {data['comparison_wins']} vs {data['comparison_losses']} comparisons"
                })
        
        return threats
    
    def _identify_opportunities(self, competitor_data: Dict, brand_mentions: int) -> List[Dict]:
        """Identify competitive opportunities"""
        opportunities = []
        
        for competitor, data in competitor_data.items():
            # Low mention count
            if data['mention_count'] < brand_mentions * 0.3:
                opportunities.append({
                    'competitor': competitor,
                    'opportunity_type': 'visibility_gap',
                    'potential': 'high',
                    'details': f"Low competitor visibility: {data['mention_count']} mentions"
                })
            
            # Losing comparisons
            if data['comparison_losses'] > data['comparison_wins']:
                opportunities.append({
                    'competitor': competitor,
                    'opportunity_type': 'comparison_advantage',
                    'potential': 'medium',
                    'details': f"Brand wins {data['comparison_losses']} vs {data['comparison_wins']} comparisons"
                })
        
        return opportunities
