from textblob import TextBlob
from typing import Dict, List
import re
import logging

logger = logging.getLogger(__name__)

class SentimentAnalyzer:
    """Analyze sentiment of brand mentions in AI responses"""
    
    def __init__(self):
        # Define sentiment lexicons
        self.positive_words = {
            'excellent': 0.8, 'amazing': 0.9, 'outstanding': 0.9, 'superior': 0.7,
            'best': 0.8, 'great': 0.7, 'fantastic': 0.8, 'wonderful': 0.8,
            'impressive': 0.7, 'reliable': 0.6, 'trusted': 0.6, 'quality': 0.6,
            'innovative': 0.7, 'leading': 0.6, 'popular': 0.5, 'recommend': 0.7,
            'love': 0.8, 'perfect': 0.9, 'top': 0.6, 'premium': 0.6,
            'professional': 0.5, 'efficient': 0.6, 'effective': 0.6, 'solid': 0.5,
            'strong': 0.6, 'good': 0.5, 'nice': 0.4, 'decent': 0.3
        }
        
        self.negative_words = {
            'terrible': -0.9, 'awful': -0.9, 'horrible': -0.9, 'worst': -0.8,
            'bad': -0.6, 'poor': -0.6, 'disappointing': -0.7, 'unreliable': -0.7,
            'expensive': -0.4, 'overpriced': -0.6, 'cheap': -0.5, 'avoid': -0.8,
            'hate': -0.9, 'problematic': -0.6, 'issues': -0.5, 'problems': -0.5,
            'complaints': -0.6, 'failed': -0.7, 'broken': -0.7, 'useless': -0.8,
            'waste': -0.7, 'regret': -0.6, 'disappointed': -0.6, 'frustrated': -0.6,
            'annoying': -0.5, 'confusing': -0.4, 'difficult': -0.4, 'slow': -0.3
        }
        
        # Context modifiers
        self.intensifiers = {
            'very': 1.5, 'extremely': 2.0, 'incredibly': 1.8, 'absolutely': 1.7,
            'really': 1.3, 'quite': 1.2, 'pretty': 1.1, 'somewhat': 0.8,
            'rather': 0.9, 'fairly': 0.9, 'totally': 1.6, 'completely': 1.7
        }
        
        self.negators = ['not', 'no', 'never', 'none', 'nothing', 'neither', 'nor']
    
    def analyze_sentiment(self, text: str, brand_name: str) -> Dict:
        """Analyze sentiment of text with focus on brand mentions"""
        # Get overall sentiment using TextBlob
        blob = TextBlob(text)
        textblob_sentiment = blob.sentiment
        
        # Get brand-specific sentiment
        brand_sentiment = self._analyze_brand_sentiment(text, brand_name)
        
        # Combine results
        return {
            'overall_polarity': textblob_sentiment.polarity,  # -1 to 1
            'overall_subjectivity': textblob_sentiment.subjectivity,  # 0 to 1
            'brand_sentiment_score': brand_sentiment['score'],
            'brand_sentiment_confidence': brand_sentiment['confidence'],
            'brand_sentiment_label': self._get_sentiment_label(brand_sentiment['score']),
            'sentiment_indicators': brand_sentiment['indicators'],
            'context_analysis': brand_sentiment['context']
        }
    
    def _analyze_brand_sentiment(self, text: str, brand_name: str) -> Dict:
        """Analyze sentiment specifically around brand mentions"""
        text_lower = text.lower()
        brand_lower = brand_name.lower()
        
        # Find brand mentions and their contexts
        brand_contexts = self._extract_brand_contexts(text, brand_name)
        
        if not brand_contexts:
            return {
                'score': 0.0,
                'confidence': 0.0,
                'indicators': [],
                'context': 'no_mention'
            }
        
        # Analyze sentiment for each context
        context_sentiments = []
        all_indicators = []
        
        for context in brand_contexts:
            context_sentiment = self._analyze_context_sentiment(context)
            context_sentiments.append(context_sentiment['score'])
            all_indicators.extend(context_sentiment['indicators'])
        
        # Calculate weighted average (give more weight to stronger sentiments)
        if context_sentiments:
            weights = [abs(score) + 0.1 for score in context_sentiments]  # Avoid zero weights
            weighted_score = sum(score * weight for score, weight in zip(context_sentiments, weights)) / sum(weights)
            confidence = min(sum(weights) / len(weights), 1.0)
        else:
            weighted_score = 0.0
            confidence = 0.0
        
        return {
            'score': weighted_score,
            'confidence': confidence,
            'indicators': list(set(all_indicators)),
            'context': 'analyzed'
        }
    
    def _extract_brand_contexts(self, text: str, brand_name: str, context_size: int = 50) -> List[str]:
        """Extract text contexts around brand mentions"""
        contexts = []
        text_lower = text.lower()
        brand_lower = brand_name.lower()
        
        # Find all brand mentions
        pattern = r'\b' + re.escape(brand_lower) + r'\b'
        
        for match in re.finditer(pattern, text_lower):
            start_pos = match.start()
            end_pos = match.end()
            
            # Extract context
            context_start = max(0, start_pos - context_size)
            context_end = min(len(text), end_pos + context_size)
            
            context = text[context_start:context_end]
            contexts.append(context)
        
        return contexts
    
    def _analyze_context_sentiment(self, context: str) -> Dict:
        """Analyze sentiment of a specific context"""
        context_lower = context.lower()
        words = re.findall(r'\b\w+\b', context_lower)
        
        sentiment_score = 0.0
        indicators = []
        
        # Check for sentiment words
        for i, word in enumerate(words):
            # Check for negation in previous 2 words
            negated = any(neg in words[max(0, i-2):i] for neg in self.negators)
            
            # Check for intensifiers in previous 2 words
            intensifier = 1.0
            for j in range(max(0, i-2), i):
                if words[j] in self.intensifiers:
                    intensifier = self.intensifiers[words[j]]
                    break
            
            # Calculate sentiment contribution
            if word in self.positive_words:
                score = self.positive_words[word] * intensifier
                if negated:
                    score = -score
                sentiment_score += score
                indicators.append(f"{'not ' if negated else ''}{word}")
            
            elif word in self.negative_words:
                score = self.negative_words[word] * intensifier
                if negated:
                    score = -score
                sentiment_score += score
                indicators.append(f"{'not ' if negated else ''}{word}")
        
        # Normalize score
        if indicators:
            sentiment_score = sentiment_score / len(indicators)
        
        # Clamp to [-1, 1]
        sentiment_score = max(-1.0, min(1.0, sentiment_score))
        
        return {
            'score': sentiment_score,
            'indicators': indicators
        }
    
    def _get_sentiment_label(self, score: float) -> str:
        """Convert sentiment score to label"""
        if score >= 0.3:
            return 'positive'
        elif score <= -0.3:
            return 'negative'
        else:
            return 'neutral'
    
    def analyze_comparative_sentiment(self, text: str, brand_name: str, competitors: List[str]) -> Dict:
        """Analyze sentiment in comparative contexts"""
        results = {}
        
        # Analyze sentiment for main brand
        brand_sentiment = self.analyze_sentiment(text, brand_name)
        results[brand_name] = brand_sentiment
        
        # Analyze sentiment for competitors
        for competitor in competitors:
            comp_sentiment = self.analyze_sentiment(text, competitor)
            results[competitor] = comp_sentiment
        
        # Calculate relative sentiment
        brand_score = brand_sentiment['brand_sentiment_score']
        competitor_scores = [results[comp]['brand_sentiment_score'] for comp in competitors if results[comp]['brand_sentiment_score'] != 0]
        
        if competitor_scores:
            avg_competitor_score = sum(competitor_scores) / len(competitor_scores)
            relative_sentiment = brand_score - avg_competitor_score
        else:
            relative_sentiment = brand_score
        
        return {
            'individual_sentiments': results,
            'relative_sentiment': relative_sentiment,
            'sentiment_advantage': relative_sentiment > 0.1,
            'sentiment_disadvantage': relative_sentiment < -0.1
        }
    
    def get_sentiment_trends(self, sentiment_history: List[Dict]) -> Dict:
        """Analyze sentiment trends over time"""
        if len(sentiment_history) < 2:
            return {'trend': 'insufficient_data', 'change': 0.0}
        
        scores = [entry['brand_sentiment_score'] for entry in sentiment_history]
        
        # Calculate trend
        if len(scores) >= 3:
            # Use linear regression for trend
            n = len(scores)
            x_sum = sum(range(n))
            y_sum = sum(scores)
            xy_sum = sum(i * score for i, score in enumerate(scores))
            x2_sum = sum(i * i for i in range(n))
            
            slope = (n * xy_sum - x_sum * y_sum) / (n * x2_sum - x_sum * x_sum)
        else:
            # Simple difference for 2 points
            slope = scores[-1] - scores[0]
        
        # Determine trend direction
        if slope > 0.05:
            trend = 'improving'
        elif slope < -0.05:
            trend = 'declining'
        else:
            trend = 'stable'
        
        return {
            'trend': trend,
            'change': slope,
            'current_score': scores[-1],
            'previous_score': scores[-2] if len(scores) > 1 else 0,
            'volatility': self._calculate_volatility(scores)
        }
    
    def _calculate_volatility(self, scores: List[float]) -> float:
        """Calculate sentiment volatility"""
        if len(scores) < 2:
            return 0.0
        
        mean_score = sum(scores) / len(scores)
        variance = sum((score - mean_score) ** 2 for score in scores) / len(scores)
        
        return variance ** 0.5
