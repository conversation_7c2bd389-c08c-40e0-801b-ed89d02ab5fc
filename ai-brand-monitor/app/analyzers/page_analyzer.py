import re
import asyncio
from typing import Dict, List, Optional, Any
from urllib.parse import urlparse, urljoin
import httpx
from bs4 import BeautifulSoup
from loguru import logger

from app.clients.openrouter_client import OpenRouterClient
from app.database.database import get_db_session
from app.database import models
from app.config import config


class PageAnalyzer:
    """AI-powered page analysis for brand monitoring setup"""
    
    def __init__(self, api_key: Optional[str] = None):
        self.openrouter_client = OpenRouterClient(api_key)
        
    async def analyze_website(
        self,
        url: str,
        brand_name: str,
        model: str = "openai/gpt-3.5-turbo"
    ) -> Dict[str, Any]:
        """Analyze website to extract brand information and generate monitoring queries"""
        
        try:
            # Scrape website content
            content = await self._scrape_website(url)
            if not content:
                return {"error": "Failed to scrape website content"}
            
            # Analyze content with AI
            analysis = await self._analyze_content_with_ai(content, brand_name, url, model)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing website {url}: {str(e)}")
            return {"error": str(e)}
    
    async def _scrape_website(self, url: str) -> Optional[str]:
        """Scrape website content"""
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(url, follow_redirects=True)
                response.raise_for_status()
                
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Remove script and style elements
                for script in soup(["script", "style", "nav", "footer", "header"]):
                    script.decompose()
                
                # Extract text content
                text_content = soup.get_text()
                
                # Clean up text
                lines = (line.strip() for line in text_content.splitlines())
                chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                text = ' '.join(chunk for chunk in chunks if chunk)
                
                # Limit content length
                if len(text) > 8000:
                    text = text[:8000] + "..."
                
                return text
                
        except Exception as e:
            logger.error(f"Error scraping website {url}: {str(e)}")
            return None
    
    async def _analyze_content_with_ai(
        self,
        content: str,
        brand_name: str,
        url: str,
        model: str
    ) -> Dict[str, Any]:
        """Analyze website content using AI"""
        
        analysis_prompt = f"""Analyze the following website content for the brand "{brand_name}" and provide a comprehensive analysis:

Website URL: {url}
Content: {content}

Please provide a JSON response with the following structure:
{{
    "business_description": "Brief description of what the business does",
    "industry_classification": "Primary industry category",
    "extracted_keywords": ["keyword1", "keyword2", "keyword3"],
    "identified_products": ["product1", "product2"],
    "identified_competitors": ["competitor1", "competitor2"],
    "brand_positioning": "How the brand positions itself in the market",
    "target_audience": "Description of target audience",
    "suggested_queries": [
        "query about brand recommendations",
        "query about product comparisons",
        "query about industry solutions"
    ],
    "confidence_score": 0.85
}}

Focus on:
1. Extract 5-10 relevant keywords that represent the brand's core business
2. Identify main products/services offered
3. Identify potential competitors mentioned or implied
4. Generate 8-12 diverse monitoring queries that would help track brand visibility
5. Provide a confidence score (0-1) for the analysis quality

Make sure the suggested queries are diverse and cover:
- Direct brand recommendations
- Product/service comparisons
- Industry-specific questions
- Problem-solving scenarios where the brand could be mentioned"""

        try:
            response_data = await self.openrouter_client.send_query(
                query=analysis_prompt,
                model=model,
                temperature=0.3,  # Lower temperature for more consistent analysis
                max_tokens=2000
            )
            
            if not response_data.get("success"):
                return {"error": f"AI analysis failed: {response_data.get('error')}"}
            
            # Parse JSON response
            response_text = response_data.get("response", "")
            
            # Try to extract JSON from response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                import json
                try:
                    analysis_result = json.loads(json_match.group())
                    
                    # Validate and clean the result
                    cleaned_result = self._clean_analysis_result(analysis_result, brand_name)
                    
                    # Add metadata
                    cleaned_result.update({
                        "analysis_model": model,
                        "url": url,
                        "cost": response_data.get("cost", 0.0),
                        "tokens_used": response_data.get("usage", {}).get("total_tokens", 0)
                    })
                    
                    return cleaned_result
                    
                except json.JSONDecodeError:
                    logger.error("Failed to parse JSON from AI response")
                    return {"error": "Failed to parse AI analysis response"}
            else:
                logger.error("No JSON found in AI response")
                return {"error": "Invalid AI response format"}
                
        except Exception as e:
            logger.error(f"Error in AI analysis: {str(e)}")
            return {"error": str(e)}
    
    def _clean_analysis_result(self, result: Dict, brand_name: str) -> Dict[str, Any]:
        """Clean and validate analysis result"""
        
        cleaned = {
            "business_description": str(result.get("business_description", "")).strip()[:500],
            "industry_classification": str(result.get("industry_classification", "")).strip()[:100],
            "extracted_keywords": [],
            "identified_products": [],
            "identified_competitors": [],
            "brand_positioning": str(result.get("brand_positioning", "")).strip()[:1000],
            "target_audience": str(result.get("target_audience", "")).strip()[:1000],
            "suggested_queries": [],
            "confidence_score": float(result.get("confidence_score", 0.5))
        }
        
        # Clean keywords
        keywords = result.get("extracted_keywords", [])
        if isinstance(keywords, list):
            cleaned["extracted_keywords"] = [
                str(kw).strip().lower() for kw in keywords[:15] 
                if str(kw).strip() and len(str(kw).strip()) > 2
            ]
        
        # Clean products
        products = result.get("identified_products", [])
        if isinstance(products, list):
            cleaned["identified_products"] = [
                str(prod).strip() for prod in products[:10]
                if str(prod).strip() and len(str(prod).strip()) > 2
            ]
        
        # Clean competitors (exclude the brand itself)
        competitors = result.get("identified_competitors", [])
        if isinstance(competitors, list):
            cleaned["identified_competitors"] = [
                str(comp).strip() for comp in competitors[:10]
                if str(comp).strip() and str(comp).strip().lower() != brand_name.lower()
                and len(str(comp).strip()) > 2
            ]
        
        # Clean queries
        queries = result.get("suggested_queries", [])
        if isinstance(queries, list):
            cleaned["suggested_queries"] = [
                str(query).strip() for query in queries[:15]
                if str(query).strip() and len(str(query).strip()) > 10
            ]
        
        # Ensure confidence score is valid
        if not 0 <= cleaned["confidence_score"] <= 1:
            cleaned["confidence_score"] = 0.5
        
        return cleaned
    
    async def save_analysis(
        self,
        brand_id: int,
        url: str,
        analysis_result: Dict[str, Any]
    ) -> Optional[models.PageAnalysis]:
        """Save analysis result to database"""
        
        try:
            with get_db_session() as db:
                # Check if analysis already exists for this URL
                existing = db.query(models.PageAnalysis).filter(
                    models.PageAnalysis.brand_id == brand_id,
                    models.PageAnalysis.url == url
                ).first()
                
                if existing:
                    # Update existing analysis
                    existing.extracted_keywords = analysis_result.get("extracted_keywords", [])
                    existing.identified_products = analysis_result.get("identified_products", [])
                    existing.identified_competitors = analysis_result.get("identified_competitors", [])
                    existing.business_description = analysis_result.get("business_description", "")
                    existing.industry_classification = analysis_result.get("industry_classification", "")
                    existing.suggested_queries = analysis_result.get("suggested_queries", [])
                    existing.brand_positioning = analysis_result.get("brand_positioning", "")
                    existing.target_audience = analysis_result.get("target_audience", "")
                    existing.analysis_model = analysis_result.get("analysis_model", "")
                    existing.confidence_score = analysis_result.get("confidence_score", 0.0)
                    
                    page_analysis = existing
                else:
                    # Create new analysis
                    page_analysis = models.PageAnalysis(
                        brand_id=brand_id,
                        url=url,
                        extracted_keywords=analysis_result.get("extracted_keywords", []),
                        identified_products=analysis_result.get("identified_products", []),
                        identified_competitors=analysis_result.get("identified_competitors", []),
                        business_description=analysis_result.get("business_description", ""),
                        industry_classification=analysis_result.get("industry_classification", ""),
                        suggested_queries=analysis_result.get("suggested_queries", []),
                        brand_positioning=analysis_result.get("brand_positioning", ""),
                        target_audience=analysis_result.get("target_audience", ""),
                        analysis_model=analysis_result.get("analysis_model", ""),
                        confidence_score=analysis_result.get("confidence_score", 0.0)
                    )
                    db.add(page_analysis)
                
                db.commit()
                db.refresh(page_analysis)
                
                return page_analysis
                
        except Exception as e:
            logger.error(f"Error saving page analysis: {str(e)}")
            return None
    
    async def generate_additional_queries(
        self,
        brand_name: str,
        industry: str,
        keywords: List[str],
        competitors: List[str],
        query_count: int = 10,
        model: str = "openai/gpt-3.5-turbo"
    ) -> List[str]:
        """Generate additional monitoring queries based on brand information"""
        
        competitors_text = f"Competitors: {', '.join(competitors[:5])}" if competitors else ""
        keywords_text = f"Key topics: {', '.join(keywords[:10])}" if keywords else ""
        
        prompt = f"""Generate {query_count} diverse monitoring queries for tracking the brand "{brand_name}" in the {industry} industry.

{competitors_text}
{keywords_text}

Create queries that would naturally lead to mentions of this brand in AI responses. Include:
- Direct recommendation requests
- Comparison questions
- Problem-solving scenarios
- Industry-specific questions
- "Best of" type questions

Format as a simple list, one query per line. Make queries natural and conversational."""

        try:
            response_data = await self.openrouter_client.send_query(
                query=prompt,
                model=model,
                temperature=0.8,
                max_tokens=1000
            )
            
            if response_data.get("success"):
                response_text = response_data.get("response", "")
                
                # Extract queries from response
                queries = []
                for line in response_text.split('\n'):
                    line = line.strip()
                    # Remove numbering, bullets, etc.
                    line = re.sub(r'^\d+\.?\s*', '', line)
                    line = re.sub(r'^[-*•]\s*', '', line)
                    
                    if line and len(line) > 10 and '?' in line:
                        queries.append(line)
                
                return queries[:query_count]
            
        except Exception as e:
            logger.error(f"Error generating additional queries: {str(e)}")
        
        return []
