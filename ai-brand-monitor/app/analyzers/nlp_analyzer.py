import re
import spacy
from typing import List, Dict, Tuple
from collections import Counter
import logging

logger = logging.getLogger(__name__)

class NLPAnalyzer:
    """Natural Language Processing analyzer for AI responses"""
    
    def __init__(self):
        try:
            # Load spaCy model (download with: python -m spacy download en_core_web_sm)
            self.nlp = spacy.load("en_core_web_sm")
        except OSError:
            logger.warning("spaCy model not found. Install with: python -m spacy download en_core_web_sm")
            self.nlp = None
    
    def extract_brand_mentions(self, text: str, brand_name: str, competitors: List[str] = None) -> List[Dict]:
        """Extract all mentions of brand and competitors from text"""
        mentions = []
        text_lower = text.lower()
        brand_lower = brand_name.lower()
        
        # Find brand mentions
        brand_mentions = self._find_mentions(text, brand_name)
        for mention in brand_mentions:
            mentions.append({
                'brand': brand_name,
                'text': mention['text'],
                'start_pos': mention['start'],
                'end_pos': mention['end'],
                'context': self._extract_context(text, mention['start'], mention['end']),
                'is_target_brand': True
            })
        
        # Find competitor mentions
        if competitors:
            for competitor in competitors:
                comp_mentions = self._find_mentions(text, competitor)
                for mention in comp_mentions:
                    mentions.append({
                        'brand': competitor,
                        'text': mention['text'],
                        'start_pos': mention['start'],
                        'end_pos': mention['end'],
                        'context': self._extract_context(text, mention['start'], mention['end']),
                        'is_target_brand': False
                    })
        
        return mentions
    
    def _find_mentions(self, text: str, brand_name: str) -> List[Dict]:
        """Find all mentions of a specific brand in text"""
        mentions = []
        brand_lower = brand_name.lower()
        text_lower = text.lower()
        
        # Simple word boundary search
        pattern = r'\b' + re.escape(brand_lower) + r'\b'
        
        for match in re.finditer(pattern, text_lower):
            mentions.append({
                'text': text[match.start():match.end()],
                'start': match.start(),
                'end': match.end()
            })
        
        return mentions
    
    def _extract_context(self, text: str, start_pos: int, end_pos: int, context_size: int = 100) -> str:
        """Extract context around a mention"""
        context_start = max(0, start_pos - context_size)
        context_end = min(len(text), end_pos + context_size)
        
        return text[context_start:context_end].strip()
    
    def analyze_mention_context(self, mention: Dict) -> Dict:
        """Analyze the context of a brand mention"""
        context = mention['context']
        analysis = {
            'mention_type': self._classify_mention_type(context),
            'sentiment_indicators': self._extract_sentiment_indicators(context),
            'comparison_context': self._detect_comparison(context),
            'authority_indicators': self._detect_authority_signals(context),
            'position_importance': self._assess_position_importance(mention)
        }
        
        return analysis
    
    def _classify_mention_type(self, context: str) -> str:
        """Classify the type of mention based on context"""
        context_lower = context.lower()
        
        # Recommendation patterns
        if any(word in context_lower for word in ['recommend', 'suggest', 'best', 'top', 'choose']):
            return 'recommendation'
        
        # Comparison patterns
        if any(word in context_lower for word in ['vs', 'versus', 'compared to', 'better than', 'worse than']):
            return 'comparison'
        
        # Review/opinion patterns
        if any(word in context_lower for word in ['review', 'opinion', 'experience', 'think', 'feel']):
            return 'review'
        
        # Factual/informational patterns
        if any(word in context_lower for word in ['is', 'are', 'has', 'have', 'offers', 'provides']):
            return 'factual'
        
        return 'general'
    
    def _extract_sentiment_indicators(self, context: str) -> Dict:
        """Extract words that indicate sentiment"""
        positive_words = [
            'excellent', 'great', 'amazing', 'outstanding', 'superior', 'best', 'top',
            'reliable', 'trusted', 'quality', 'innovative', 'leading', 'popular',
            'recommend', 'love', 'perfect', 'fantastic', 'wonderful', 'impressive'
        ]
        
        negative_words = [
            'terrible', 'awful', 'bad', 'worst', 'poor', 'disappointing', 'unreliable',
            'expensive', 'overpriced', 'cheap', 'low-quality', 'avoid', 'hate',
            'problematic', 'issues', 'problems', 'complaints', 'failed', 'broken'
        ]
        
        context_lower = context.lower()
        
        found_positive = [word for word in positive_words if word in context_lower]
        found_negative = [word for word in negative_words if word in context_lower]
        
        return {
            'positive_indicators': found_positive,
            'negative_indicators': found_negative,
            'positive_count': len(found_positive),
            'negative_count': len(found_negative)
        }
    
    def _detect_comparison(self, context: str) -> Dict:
        """Detect if mention is in a comparison context"""
        comparison_patterns = [
            r'(\w+)\s+(?:is|are)\s+better\s+than\s+(\w+)',
            r'(\w+)\s+vs\.?\s+(\w+)',
            r'compared\s+to\s+(\w+)',
            r'(\w+)\s+outperforms?\s+(\w+)',
            r'choose\s+(\w+)\s+over\s+(\w+)'
        ]
        
        context_lower = context.lower()
        comparisons = []
        
        for pattern in comparison_patterns:
            matches = re.findall(pattern, context_lower)
            comparisons.extend(matches)
        
        return {
            'is_comparison': len(comparisons) > 0,
            'comparison_pairs': comparisons
        }
    
    def _detect_authority_signals(self, context: str) -> List[str]:
        """Detect signals that indicate brand authority"""
        authority_signals = [
            'leader', 'leading', 'market leader', 'industry leader', 'pioneer',
            'established', 'trusted', 'reputable', 'well-known', 'popular',
            'dominant', 'top brand', 'number one', '#1', 'first choice',
            'go-to', 'standard', 'benchmark', 'gold standard'
        ]
        
        context_lower = context.lower()
        found_signals = [signal for signal in authority_signals if signal in context_lower]
        
        return found_signals
    
    def _assess_position_importance(self, mention: Dict) -> str:
        """Assess the importance of mention based on its position"""
        start_pos = mention['start_pos']
        text_length = len(mention.get('full_text', ''))
        
        if text_length == 0:
            return 'unknown'
        
        position_ratio = start_pos / text_length
        
        if position_ratio < 0.2:
            return 'high'  # Early in response
        elif position_ratio < 0.5:
            return 'medium'  # Middle of response
        else:
            return 'low'  # Late in response
    
    def extract_keywords(self, text: str, top_n: int = 10) -> List[Tuple[str, int]]:
        """Extract important keywords from text"""
        if not self.nlp:
            # Fallback to simple word counting
            words = re.findall(r'\b\w+\b', text.lower())
            # Filter out common stop words
            stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should'}
            filtered_words = [word for word in words if word not in stop_words and len(word) > 2]
            return Counter(filtered_words).most_common(top_n)
        
        # Use spaCy for better keyword extraction
        doc = self.nlp(text)
        
        # Extract meaningful tokens (nouns, adjectives, proper nouns)
        keywords = []
        for token in doc:
            if (token.pos_ in ['NOUN', 'ADJ', 'PROPN'] and 
                not token.is_stop and 
                not token.is_punct and 
                len(token.text) > 2):
                keywords.append(token.lemma_.lower())
        
        return Counter(keywords).most_common(top_n)
    
    def analyze_response_structure(self, text: str) -> Dict:
        """Analyze the structure and characteristics of the response"""
        sentences = text.split('.')
        paragraphs = text.split('\n\n')
        
        return {
            'word_count': len(text.split()),
            'sentence_count': len([s for s in sentences if s.strip()]),
            'paragraph_count': len([p for p in paragraphs if p.strip()]),
            'avg_sentence_length': len(text.split()) / max(len(sentences), 1),
            'has_lists': bool(re.search(r'^\s*[-*•]\s', text, re.MULTILINE)),
            'has_numbers': bool(re.search(r'\d+', text)),
            'question_count': text.count('?'),
            'exclamation_count': text.count('!')
        }
