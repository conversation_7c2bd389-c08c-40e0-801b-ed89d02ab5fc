from typing import Dict, List
import math
from datetime import datetime, timedelta

class ScoringEngine:
    """Calculate various scores for brand monitoring"""
    
    def __init__(self):
        # Scoring weights
        self.visibility_weights = {
            'mention_frequency': 0.4,
            'position_importance': 0.3,
            'context_relevance': 0.2,
            'platform_diversity': 0.1
        }
        
        self.authority_weights = {
            'recommendation_mentions': 0.3,
            'leadership_signals': 0.25,
            'expertise_indicators': 0.2,
            'trust_signals': 0.15,
            'comparison_wins': 0.1
        }
    
    def calculate_visibility_score(self, mentions: List[Dict], total_queries: int) -> float:
        """Calculate visibility score (0-100)"""
        if total_queries == 0:
            return 0.0
        
        # Base visibility from mention frequency
        mention_rate = len(mentions) / total_queries
        frequency_score = min(mention_rate * 100, 100)
        
        # Position importance score
        position_scores = []
        for mention in mentions:
            pos_score = self._calculate_position_score(mention)
            position_scores.append(pos_score)
        
        avg_position_score = sum(position_scores) / len(position_scores) if position_scores else 0
        
        # Context relevance score
        relevance_scores = []
        for mention in mentions:
            rel_score = self._calculate_relevance_score(mention)
            relevance_scores.append(rel_score)
        
        avg_relevance_score = sum(relevance_scores) / len(relevance_scores) if relevance_scores else 0
        
        # Platform diversity score
        platforms = set(mention.get('platform', 'unknown') for mention in mentions)
        diversity_score = min(len(platforms) * 33.33, 100)  # Max 3 platforms
        
        # Weighted final score
        visibility_score = (
            frequency_score * self.visibility_weights['mention_frequency'] +
            avg_position_score * self.visibility_weights['position_importance'] +
            avg_relevance_score * self.visibility_weights['context_relevance'] +
            diversity_score * self.visibility_weights['platform_diversity']
        )
        
        return round(min(visibility_score, 100), 2)
    
    def calculate_sentiment_score(self, mentions: List[Dict]) -> float:
        """Calculate sentiment score (-100 to +100)"""
        if not mentions:
            return 0.0
        
        sentiment_scores = []
        confidence_weights = []
        
        for mention in mentions:
            sentiment = mention.get('sentiment_score', 0)
            confidence = mention.get('confidence_score', 0.5)
            
            sentiment_scores.append(sentiment)
            confidence_weights.append(confidence)
        
        # Calculate weighted average
        if confidence_weights:
            weighted_sentiment = sum(
                score * weight for score, weight in zip(sentiment_scores, confidence_weights)
            ) / sum(confidence_weights)
        else:
            weighted_sentiment = sum(sentiment_scores) / len(sentiment_scores)
        
        # Convert to -100 to +100 scale
        return round(weighted_sentiment * 100, 2)
    
    def calculate_authority_score(self, mentions: List[Dict], responses: List[Dict]) -> float:
        """Calculate authority score (0-100)"""
        if not mentions:
            return 0.0
        
        # Count recommendation mentions
        recommendation_count = sum(
            1 for mention in mentions 
            if mention.get('mention_type') == 'recommendation'
        )
        recommendation_score = min(recommendation_count * 20, 100)
        
        # Count leadership signals
        leadership_signals = 0
        for mention in mentions:
            context = mention.get('context', '').lower()
            if any(signal in context for signal in ['leader', 'leading', 'top', 'best', '#1']):
                leadership_signals += 1
        
        leadership_score = min(leadership_signals * 25, 100)
        
        # Count expertise indicators
        expertise_count = 0
        for mention in mentions:
            context = mention.get('context', '').lower()
            if any(indicator in context for indicator in ['expert', 'professional', 'specialist', 'authority']):
                expertise_count += 1
        
        expertise_score = min(expertise_count * 30, 100)
        
        # Count trust signals
        trust_count = 0
        for mention in mentions:
            context = mention.get('context', '').lower()
            if any(signal in context for signal in ['trusted', 'reliable', 'reputable', 'established']):
                trust_count += 1
        
        trust_score = min(trust_count * 25, 100)
        
        # Count comparison wins
        comparison_wins = sum(
            1 for mention in mentions 
            if mention.get('comparison_result') == 'wins'
        )
        comparison_score = min(comparison_wins * 20, 100)
        
        # Weighted final score
        authority_score = (
            recommendation_score * self.authority_weights['recommendation_mentions'] +
            leadership_score * self.authority_weights['leadership_signals'] +
            expertise_score * self.authority_weights['expertise_indicators'] +
            trust_score * self.authority_weights['trust_signals'] +
            comparison_score * self.authority_weights['comparison_wins']
        )
        
        return round(min(authority_score, 100), 2)
    
    def calculate_share_of_voice(self, brand_mentions: int, competitor_mentions: Dict[str, int]) -> float:
        """Calculate share of voice percentage"""
        total_mentions = brand_mentions + sum(competitor_mentions.values())
        
        if total_mentions == 0:
            return 0.0
        
        share = (brand_mentions / total_mentions) * 100
        return round(share, 2)
    
    def calculate_trend_score(self, historical_data: List[Dict], metric: str = 'visibility_score') -> float:
        """Calculate trend score based on historical data"""
        if len(historical_data) < 2:
            return 0.0
        
        # Sort by date
        sorted_data = sorted(historical_data, key=lambda x: x.get('date', datetime.min))
        
        # Get recent values
        recent_values = [entry.get(metric, 0) for entry in sorted_data[-5:]]  # Last 5 data points
        
        if len(recent_values) < 2:
            return 0.0
        
        # Calculate trend using linear regression
        n = len(recent_values)
        x_values = list(range(n))
        
        # Calculate slope
        x_mean = sum(x_values) / n
        y_mean = sum(recent_values) / n
        
        numerator = sum((x - x_mean) * (y - y_mean) for x, y in zip(x_values, recent_values))
        denominator = sum((x - x_mean) ** 2 for x in x_values)
        
        if denominator == 0:
            return 0.0
        
        slope = numerator / denominator
        
        # Convert slope to trend score (-100 to +100)
        # Normalize based on the scale of the metric
        max_change = 10  # Assume max reasonable change per period
        trend_score = (slope / max_change) * 100
        
        return round(max(-100, min(100, trend_score)), 2)
    
    def _calculate_position_score(self, mention: Dict) -> float:
        """Calculate score based on mention position in response"""
        position = mention.get('position_in_response', 0)
        response_length = mention.get('response_length', 1000)  # Assume default length
        
        if response_length == 0:
            return 0.0
        
        # Earlier mentions get higher scores
        position_ratio = position / response_length
        
        if position_ratio < 0.2:
            return 100  # Very early mention
        elif position_ratio < 0.4:
            return 80   # Early mention
        elif position_ratio < 0.6:
            return 60   # Middle mention
        elif position_ratio < 0.8:
            return 40   # Late mention
        else:
            return 20   # Very late mention
    
    def _calculate_relevance_score(self, mention: Dict) -> float:
        """Calculate relevance score based on mention context"""
        mention_type = mention.get('mention_type', 'general')
        is_primary_focus = mention.get('is_primary_focus', False)
        
        base_score = 50
        
        # Adjust based on mention type
        type_multipliers = {
            'recommendation': 1.5,
            'comparison': 1.3,
            'review': 1.2,
            'factual': 1.0,
            'general': 0.8
        }
        
        multiplier = type_multipliers.get(mention_type, 1.0)
        
        # Boost if brand is primary focus
        if is_primary_focus:
            multiplier *= 1.3
        
        relevance_score = base_score * multiplier
        return min(relevance_score, 100)
    
    def calculate_competitive_index(self, brand_score: float, competitor_scores: List[float]) -> float:
        """Calculate competitive index comparing brand to competitors"""
        if not competitor_scores:
            return 100.0  # No competition data
        
        avg_competitor_score = sum(competitor_scores) / len(competitor_scores)
        
        if avg_competitor_score == 0:
            return 200.0 if brand_score > 0 else 100.0
        
        # Index where 100 = same as competitors, >100 = better, <100 = worse
        competitive_index = (brand_score / avg_competitor_score) * 100
        
        return round(min(competitive_index, 200), 2)  # Cap at 200%
