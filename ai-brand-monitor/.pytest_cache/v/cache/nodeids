["tests/test_openrouter_client.py::TestOpenRouterClient::test_available_models_structure", "tests/test_openrouter_client.py::TestOpenRouterClient::test_calculate_cost", "tests/test_openrouter_client.py::TestOpenRouterClient::test_client_initialization", "tests/test_openrouter_client.py::TestOpenRouterClient::test_client_initialization_default_key", "tests/test_openrouter_client.py::TestOpenRouterClient::test_get_available_models", "tests/test_openrouter_client.py::TestOpenRouterClient::test_get_model_info", "tests/test_openrouter_client.py::TestOpenRouterClient::test_get_supported_models", "tests/test_openrouter_client.py::TestOpenRouterClient::test_headers_configuration", "tests/test_openrouter_client.py::TestOpenRouterClient::test_rate_limiting", "tests/test_openrouter_client.py::TestOpenRouterClient::test_send_batch_queries", "tests/test_openrouter_client.py::TestOpenRouterClient::test_send_query_http_error", "tests/test_openrouter_client.py::TestOpenRouterClient::test_send_query_network_error", "tests/test_openrouter_client.py::TestOpenRouterClient::test_send_query_success", "tests/test_openrouter_client.py::TestOpenRouterClient::test_send_query_with_system_prompt", "tests/test_openrouter_client.py::TestOpenRouterClient::test_test_connection_failure", "tests/test_openrouter_client.py::TestOpenRouterClient::test_test_connection_success", "tests/test_openrouter_client.py::test_integration_with_real_api"]