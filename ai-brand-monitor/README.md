# AI Brand Monitor

A comprehensive platform for monitoring and optimizing brand visibility in AI chatbots (<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>). Track how your brand is presented by AI, compare with competitors, and receive actionable recommendations.

## 🚀 Features

### Core Monitoring (NEW: OpenRouter API Integration)
- **Multi-Model AI Monitoring**: Monitor 20+ AI models via OpenRouter API (GPT-4, Claude 3, Llama 2, PaLM 2, etc.)
- **Intelligent Website Analysis**: AI-powered analysis of your website to extract keywords, competitors, and generate monitoring queries
- **Advanced Brand Mention Detection**: Enhanced NLP to identify and analyze brand mentions across different AI models
- **Real-time Sentiment Analysis**: Track positive, negative, and neutral sentiment trends with confidence scoring

### Analytics & Scoring
- **Visibility Score (0-100)**: Overall brand visibility across AI platforms
- **Sentiment Score (-100 to +100)**: How positively AI presents your brand
- **Authority Score (0-100)**: Whether AI recognizes your brand as a leader
- **Share of Voice**: Your brand's mention percentage vs competitors

### Competitive Intelligence
- **Competitor Tracking**: Monitor how competitors are mentioned
- **Comparison Analysis**: Track head-to-head brand comparisons
- **Market Positioning**: Understand your competitive landscape
- **Opportunity Identification**: Find gaps in competitor coverage

### OpenRouter API Integration (NEW)
- **Multi-Model Access**: Monitor 20+ AI models through single API
- **Cost Management**: Transparent pricing with usage tracking and budgets
- **Personal API Keys**: Users can add their own OpenRouter API keys
- **Tier-Based Limits**: Free, Premium, and Enterprise access levels

### Alerts & Reporting
- **Smart Alerts**: AI-powered alerts for visibility drops and sentiment changes
- **Enhanced Reports**: Multi-model comparison reports with cost analysis
- **Admin Dashboard**: Comprehensive management panel for models and users
- **Advanced Analytics**: Usage statistics, cost optimization, and performance metrics

## 🏗️ Architecture

```
ai-brand-monitor/
├── app/
│   ├── database/          # SQLAlchemy models and database management
│   ├── clients/           # OpenRouter API client and AI model monitor (NEW)
│   ├── scrapers/          # Legacy AI platform scrapers (deprecated)
│   ├── analyzers/         # NLP, sentiment, scoring, and page analysis (enhanced)
│   ├── admin/             # Model management and admin tools (NEW)
│   ├── scheduler/         # Enhanced AI monitoring scheduler (NEW)
│   ├── api/              # FastAPI REST endpoints (enhanced)
│   ├── web/              # Streamlit dashboard (enhanced)
│   ├── utils/            # API usage tracking and utilities (enhanced)
│   ├── optimization/     # Recommendation engine
│   └── notifications/    # Email and webhook notifications
├── data/                 # SQLite database and exports
├── logs/                 # Application logs
├── static/               # Static web assets
└── tests/                # Test suites
```

## 🛠️ Installation

### Prerequisites
- Python 3.11+
- OpenRouter API key (get one at [openrouter.ai](https://openrouter.ai))
- Git

### Quick Start with Docker

1. **Clone the repository**
```bash
git clone <repository-url>
cd ai-brand-monitor
```

2. **Configure environment**
```bash
cp .env.example .env
# Edit .env with your OpenRouter API key and other configuration
```

3. **Start with Docker Compose**
```bash
docker-compose up -d
```

4. **Access the application**
- API: http://localhost:8000
- Dashboard: http://localhost:8501
- API Documentation: http://localhost:8000/docs

### Manual Installation

1. **Clone and setup**
```bash
git clone <repository-url>
cd ai-brand-monitor
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
python -m spacy download en_core_web_sm
```

3. **Initialize database**
```bash
python -m app.database.migrations
```

4. **Start the API server**
```bash
python -m app.main
```

5. **Start the dashboard (in another terminal)**
```bash
streamlit run app/web/dashboard.py
```

## 📊 Usage

### 1. Create Your First Brand

```python
import requests

# Register user
response = requests.post("http://localhost:8000/api/v1/register", json={
    "email": "<EMAIL>",
    "password": "your-password",
    "full_name": "Your Name"
})

# Login
response = requests.post("http://localhost:8000/api/v1/token", data={
    "username": "<EMAIL>",
    "password": "your-password"
})
token = response.json()["access_token"]

# Create brand
headers = {"Authorization": f"Bearer {token}"}
brand_data = {
    "name": "Your Brand",
    "description": "Brand description",
    "industry": "Technology",
    "keywords": ["brand", "product", "service"],
    "competitors": ["Competitor A", "Competitor B"]
}
response = requests.post("http://localhost:8000/api/v1/brands", 
                        json=brand_data, headers=headers)
```

### 2. Configure OpenRouter API

```python
# Set your OpenRouter API key
api_key_data = {"api_key": "your-openrouter-api-key"}
response = requests.put("http://localhost:8000/api/v1/users/me/openrouter-key",
                       json=api_key_data, headers=headers)
```

### 3. Analyze Your Website (NEW)

```python
# Analyze your website to extract keywords and generate queries
response = requests.post("http://localhost:8000/api/v1/brands/1/analyze-page",
                        params={"url": "https://yourbrand.com"}, headers=headers)
analysis = response.json()["analysis"]
```

### 4. Start AI Monitoring

```python
# Start AI monitoring across multiple models
monitoring_request = {
    "queries": [
        "What are the best solutions for [your industry]?",
        "Can you recommend companies in [your industry]?",
        "What should I know about [your brand]?"
    ],
    "models": ["openai/gpt-3.5-turbo", "anthropic/claude-3-haiku"]
}
response = requests.post("http://localhost:8000/api/v1/brands/1/monitor-ai",
                        json=monitoring_request, headers=headers)
```

### 5. View Results

Access the enhanced Streamlit dashboard at http://localhost:8501 to:
- View multi-model scores and trends
- Analyze brand mentions across different AI models
- Monitor API usage and costs
- Compare performance between AI models
- Set up intelligent alerts and notifications
- Manage your OpenRouter API configuration

## 🔧 Configuration

### Environment Variables

```bash
# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
SECRET_KEY=your-secret-key

# Database
DATABASE_URL=sqlite:///./data/brand_monitor.db

# OpenRouter API Configuration (NEW)
OPENROUTER_API_KEY=your-openrouter-api-key
OPENROUTER_DEFAULT_MODEL=openai/gpt-3.5-turbo
OPENROUTER_RATE_LIMIT=60
OPENROUTER_MAX_TOKENS=1000
OPENROUTER_TEMPERATURE=0.7
APP_URL=http://localhost:8000

# User API Limits (NEW)
FREE_TIER_DAILY_LIMIT=50
PREMIUM_TIER_DAILY_LIMIT=500
ENTERPRISE_TIER_DAILY_LIMIT=2000

# Legacy Scraping (deprecated)
SCRAPING_DELAY=5
MAX_RETRIES=3

# Email Notifications
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
```

### OpenRouter API Setup

1. **Get your API key**:
   - Visit [openrouter.ai](https://openrouter.ai)
   - Sign up and get your API key
   - Add credits to your account

2. **Configure in application**:
   - Add key to `.env` file or
   - Set it in user settings via dashboard

### Legacy Platform Credentials (deprecated)

For legacy scraper functionality (will be removed):
```bash
CHATGPT_EMAIL=<EMAIL>
CHATGPT_PASSWORD=your-password

CLAUDE_EMAIL=<EMAIL>
CLAUDE_PASSWORD=your-password
```

## 📈 API Endpoints

### Authentication
- `POST /api/v1/register` - Register new user
- `POST /api/v1/token` - Login and get access token

### Brands
- `GET /api/v1/brands` - List user's brands
- `POST /api/v1/brands` - Create new brand
- `GET /api/v1/brands/{id}` - Get brand details
- `PUT /api/v1/brands/{id}` - Update brand
- `DELETE /api/v1/brands/{id}` - Delete brand

### OpenRouter Integration (NEW)
- `GET /api/v1/openrouter/models` - Get available AI models
- `POST /api/v1/openrouter/test-connection` - Test API connection
- `POST /api/v1/brands/{id}/analyze-page` - Analyze website with AI
- `POST /api/v1/brands/{id}/monitor-ai` - Start AI monitoring
- `GET /api/v1/users/me/api-usage` - Get API usage statistics
- `PUT /api/v1/users/me/openrouter-key` - Update OpenRouter API key

### Admin Endpoints (NEW)
- `GET /api/v1/admin/models` - Manage AI models (admin only)
- `POST /api/v1/admin/models/sync` - Sync models from OpenRouter
- `PUT /api/v1/admin/models/{id}` - Update model configuration
- `GET /api/v1/admin/usage-stats` - Get usage statistics
- `GET /api/v1/admin/users-usage` - Get user usage summary

### Legacy Monitoring (deprecated)
- `POST /api/v1/brands/{id}/scan` - Start brand scan
- `GET /api/v1/brands/{id}/scan/{scan_id}` - Get scan status
- `GET /api/v1/brands/{id}/mentions` - Get brand mentions
- `GET /api/v1/brands/{id}/analytics` - Get analytics data

### Dashboard
- `GET /api/v1/brands/{id}/dashboard` - Get dashboard data
- `GET /api/v1/brands/{id}/alerts` - Get brand alerts

## 🧪 Testing

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app

# Run specific test file
pytest tests/test_scrapers.py
```

## 🚀 Deployment

### Production Deployment

1. **Prepare environment**
```bash
# Set production environment variables
export SECRET_KEY="your-production-secret-key"
export DATABASE_URL="postgresql://user:pass@localhost/dbname"
```

2. **Deploy with Docker**
```bash
docker-compose -f docker-compose.prod.yml up -d
```

3. **Set up reverse proxy (Nginx)**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location / {
        proxy_pass http://localhost:8501;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### Scaling Considerations

- Use PostgreSQL for production database
- Implement Redis for caching and job queues
- Set up monitoring with Prometheus/Grafana
- Use load balancer for multiple instances
- Implement rate limiting for API endpoints

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- 📧 Email: <EMAIL>
- 📖 Documentation: [docs.ai-brand-monitor.com](https://docs.ai-brand-monitor.com)
- 🐛 Issues: [GitHub Issues](https://github.com/your-repo/ai-brand-monitor/issues)

## 🗺️ Roadmap

### Completed ✅
- [x] OpenRouter API integration with 20+ AI models
- [x] AI-powered website analysis and query generation
- [x] Multi-model monitoring and comparison
- [x] Advanced usage tracking and cost management
- [x] Tier-based access control and quotas
- [x] Enhanced admin panel for model management

### In Progress 🚧
- [ ] Real-time monitoring dashboard
- [ ] Advanced analytics and insights
- [ ] Mobile app for monitoring on-the-go
- [ ] Custom model fine-tuning support

### Planned 📋
- [ ] Integration with more AI platforms (Bard, Bing Chat)
- [ ] Machine learning-based recommendation engine
- [ ] Integration with social media monitoring
- [ ] Advanced reporting and visualization
- [ ] Multi-language support
- [ ] Enterprise features (SSO, advanced permissions)
- [ ] Webhook integrations and API automation
- [ ] Advanced competitor intelligence features

---

**AI Brand Monitor** - Monitor your brand's AI presence and stay ahead of the competition! 🚀
