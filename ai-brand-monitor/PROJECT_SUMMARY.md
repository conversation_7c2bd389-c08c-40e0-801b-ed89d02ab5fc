# AI Brand Monitor - Project Summary

## 🎯 Project Overview

**AI Brand Monitor** is a comprehensive platform designed to monitor and optimize brand visibility in AI chatbots (<PERSON><PERSON><PERSON><PERSON>, <PERSON>, Perplex<PERSON>). The system helps companies track how their brand is presented by AI, compare with competitors, and receive actionable recommendations for improving their AI presence.

## 🏗️ Architecture & Components

### Core Components

1. **Web Scrapers** (`app/scrapers/`)
   - `base_scraper.py` - Abstract base class with anti-detection measures
   - `chatgpt_scraper.py` - ChatGPT response scraper
   - `claude_scraper.py` - Claude AI response scraper  
   - `perplexity_scraper.py` - Perplexity AI response scraper
   - `query_generator.py` - Intelligent query generation

2. **Analysis Engine** (`app/analyzers/`)
   - `nlp_analyzer.py` - Natural language processing and mention extraction
   - `sentiment.py` - Advanced sentiment analysis with brand focus
   - `scoring.py` - Multi-dimensional scoring system
   - `competitor.py` - Competitive analysis and positioning

3. **API Layer** (`app/api/`)
   - `routes.py` - FastAPI REST endpoints
   - `auth.py` - JWT authentication and authorization
   - `schemas.py` - Pydantic data models

4. **Database Layer** (`app/database/`)
   - `models.py` - SQLAlchemy ORM models
   - `database.py` - Database connection and session management
   - `migrations.py` - Database initialization and sample data

5. **Web Interface** (`app/web/`)
   - `dashboard.py` - Streamlit-based interactive dashboard

6. **Utilities** (`app/utils/`)
   - `logger.py` - Centralized logging with Loguru
   - `helpers.py` - Common utility functions

### Database Schema

**Core Tables:**
- `brands` - Brand information and configuration
- `users` - User accounts and authentication
- `user_brands` - User-brand access control
- `queries` - Generated queries for monitoring
- `ai_responses` - Raw AI platform responses
- `mentions` - Extracted brand mentions with context
- `analytics` - Calculated scores and metrics
- `alerts` - System notifications and warnings
- `scan_history` - Monitoring job tracking

## 📊 Key Features

### Monitoring Capabilities
- **Multi-Platform Scraping**: Automated monitoring across ChatGPT, Claude, and Perplexity
- **Intelligent Query Generation**: Context-aware query creation based on brand, industry, and keywords
- **Real-time Mention Detection**: Advanced NLP to identify brand mentions in AI responses
- **Sentiment Tracking**: Brand-specific sentiment analysis with confidence scoring

### Analytics & Scoring
- **Visibility Score (0-100)**: Measures overall brand presence in AI responses
- **Sentiment Score (-100 to +100)**: Tracks emotional tone of brand mentions
- **Authority Score (0-100)**: Evaluates brand recognition as industry leader
- **Share of Voice**: Percentage of mentions vs competitors

### Competitive Intelligence
- **Competitor Tracking**: Monitor how competitors are mentioned and positioned
- **Comparison Analysis**: Track direct brand comparisons and outcomes
- **Market Positioning**: Understand competitive landscape and opportunities
- **Trend Analysis**: Historical performance and trajectory tracking

### User Interface
- **Interactive Dashboard**: Real-time metrics and visualizations
- **Alert System**: Notifications for significant changes or issues
- **Report Generation**: Automated and on-demand reporting
- **Multi-user Support**: Role-based access control

## 🛠️ Technology Stack

### Backend
- **FastAPI** - Modern, fast web framework for APIs
- **SQLAlchemy** - SQL toolkit and ORM
- **SQLite/PostgreSQL** - Database storage
- **Pydantic** - Data validation and serialization
- **JWT** - Authentication and authorization

### Scraping & Analysis
- **Selenium** - Web browser automation
- **spaCy** - Advanced NLP processing
- **TextBlob** - Sentiment analysis
- **scikit-learn** - Machine learning utilities
- **Pandas/NumPy** - Data manipulation and analysis

### Frontend
- **Streamlit** - Interactive web dashboard
- **Plotly** - Data visualization and charts
- **HTML/CSS** - Custom styling and components

### Infrastructure
- **Docker** - Containerization and deployment
- **Uvicorn** - ASGI server
- **Loguru** - Advanced logging
- **APScheduler** - Background job scheduling

## 📁 Project Structure

```
ai-brand-monitor/
├── app/                          # Main application code
│   ├── database/                 # Database models and management
│   ├── scrapers/                 # AI platform scrapers
│   ├── analyzers/                # Analysis and scoring engines
│   ├── api/                      # REST API endpoints
│   ├── web/                      # Streamlit dashboard
│   ├── optimization/             # Recommendation engine
│   ├── notifications/            # Alert and notification system
│   ├── utils/                    # Utility functions
│   ├── config.py                 # Configuration management
│   └── main.py                   # FastAPI application entry point
├── data/                         # Database and exported data
├── logs/                         # Application logs
├── static/                       # Static web assets
├── tests/                        # Test suites
├── requirements.txt              # Python dependencies
├── Dockerfile                    # Docker container configuration
├── docker-compose.yml           # Multi-container setup
├── setup.py                     # Project initialization script
├── run.py                       # Application runner
├── .env.example                 # Environment configuration template
├── README.md                    # Project documentation
├── INSTALLATION.md              # Installation guide
└── PROJECT_SUMMARY.md           # This file
```

## 🚀 Getting Started

### Quick Installation
```bash
# Install dependencies
pip install -r requirements.txt
python -m spacy download en_core_web_sm

# Initialize project
python setup.py

# Start application
python run.py
```

### Docker Installation
```bash
docker-compose up -d
```

### Access Points
- **API**: http://localhost:8000
- **Dashboard**: http://localhost:8501
- **Documentation**: http://localhost:8000/docs

## 🔧 Configuration

### Environment Variables
- `SECRET_KEY` - JWT signing key
- `DATABASE_URL` - Database connection string
- `SMTP_*` - Email notification settings
- `SCRAPING_DELAY` - Rate limiting for scrapers

### Default Credentials
- **Email**: <EMAIL>
- **Password**: admin123

## 🧪 Testing

The project includes comprehensive test suites:
- **API Tests** (`tests/test_api.py`) - Endpoint functionality
- **Analyzer Tests** (`tests/test_analyzers.py`) - NLP and scoring logic

Run tests with: `pytest`

## 📈 Scoring Methodology

### Visibility Score
- Mention frequency (40%)
- Position importance (30%)
- Context relevance (20%)
- Platform diversity (10%)

### Sentiment Score
- Weighted sentiment analysis
- Context-aware scoring
- Confidence-based aggregation
- Brand-specific focus

### Authority Score
- Recommendation mentions (30%)
- Leadership signals (25%)
- Expertise indicators (20%)
- Trust signals (15%)
- Comparison wins (10%)

## 🔮 Future Enhancements

- Integration with additional AI platforms (Bard, Bing Chat)
- Advanced ML-based recommendation engine
- Real-time streaming analytics
- Mobile application
- Enterprise features (SSO, advanced permissions)
- Multi-language support
- Social media integration

## 📄 License

MIT License - See LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Implement changes with tests
4. Submit pull request

---

**AI Brand Monitor** provides comprehensive insights into your brand's AI presence, helping you stay competitive in the age of AI-powered search and recommendations.
