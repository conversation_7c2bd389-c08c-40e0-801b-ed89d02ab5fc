# Changelog

All notable changes to AI Brand Monitor will be documented in this file.

## [2.0.0] - 2024-01-15 - OpenRouter API Integration

### 🚀 Major Features Added

#### OpenRouter API Integration
- **Multi-Model Monitoring**: Added support for 20+ AI models via OpenRouter API
- **Cost Management**: Transparent pricing with real-time usage tracking
- **Personal API Keys**: Users can add their own OpenRouter API keys
- **Tier-Based Access**: Free, Premium, and Enterprise access levels with different limits

#### AI-Powered Website Analysis
- **Intelligent Page Analysis**: Automatic extraction of keywords, competitors, and products
- **Query Generation**: AI-powered generation of monitoring queries based on website content
- **Brand Positioning**: Analysis of brand positioning and target audience
- **Competitor Detection**: Automatic identification of competitors from website content

#### Enhanced Admin Panel
- **Model Management**: Configure and manage available AI models
- **Usage Statistics**: Comprehensive analytics on API usage and costs
- **User Management**: Monitor user activity and manage quotas
- **Cost Optimization**: Tools for analyzing and optimizing API costs

#### Advanced Usage Tracking
- **Real-Time Monitoring**: Track API usage, costs, and performance in real-time
- **Budget Management**: Set monthly budgets and receive alerts
- **Quota Management**: Flexible daily and monthly limits per user and model
- **Usage Analytics**: Detailed breakdowns by model, brand, and time period

### 🔧 Technical Improvements

#### New Components
- `app/clients/openrouter_client.py` - OpenRouter API client with rate limiting
- `app/clients/ai_model_monitor.py` - New monitoring system replacing scrapers
- `app/analyzers/page_analyzer.py` - AI-powered website analysis
- `app/admin/model_management.py` - Admin tools for model management
- `app/utils/api_usage_tracker.py` - Comprehensive usage tracking
- `app/scheduler/ai_scheduler.py` - Enhanced scheduler for AI monitoring

#### Database Schema Updates
- Added `ai_models` table for model configuration
- Added `api_usage` table for usage tracking
- Added `page_analyses` table for website analysis results
- Extended `users` table with API configuration fields
- Enhanced `ai_responses` table with cost and token tracking

#### API Enhancements
- New OpenRouter integration endpoints
- Enhanced user management endpoints
- Admin-only model management endpoints
- Usage statistics and analytics endpoints

### 📊 Dashboard Improvements

#### Enhanced User Interface
- **Multi-Model Analytics**: Compare performance across different AI models
- **Cost Dashboard**: Real-time cost tracking and budget monitoring
- **Usage Statistics**: Detailed usage analytics and trends
- **API Configuration**: Easy setup and testing of OpenRouter API keys

#### New Features
- Website analysis interface
- Model selection for monitoring
- Usage quota visualization
- Cost optimization recommendations

### 🔄 Migration and Compatibility

#### Backward Compatibility
- All existing API endpoints continue to work
- Legacy scraper functionality maintained (deprecated)
- Existing data and analytics preserved
- Smooth migration path for existing users

#### Migration Tools
- Automatic database schema migration
- Default model configuration setup
- User data preservation
- Legacy data conversion utilities

### 🛠️ Configuration Changes

#### New Environment Variables
```bash
OPENROUTER_API_KEY=your-openrouter-api-key
OPENROUTER_DEFAULT_MODEL=openai/gpt-3.5-turbo
OPENROUTER_RATE_LIMIT=60
FREE_TIER_DAILY_LIMIT=50
PREMIUM_TIER_DAILY_LIMIT=500
ENTERPRISE_TIER_DAILY_LIMIT=2000
```

#### Updated Dependencies
- Added `httpx` for async HTTP requests
- Added `aiohttp` for enhanced async support
- Added `asyncio-throttle` for rate limiting

### 📈 Performance Improvements

#### Scalability Enhancements
- **Async Processing**: Full async support for better performance
- **Rate Limiting**: Intelligent rate limiting to prevent API overuse
- **Batch Processing**: Efficient handling of multiple monitoring requests
- **Resource Optimization**: Reduced memory and CPU usage compared to scrapers

#### Reliability Improvements
- **Error Handling**: Enhanced error handling and recovery
- **Retry Logic**: Intelligent retry mechanisms for failed requests
- **Monitoring**: Better monitoring and alerting for system health
- **Logging**: Comprehensive logging for debugging and analysis

### 🔒 Security Enhancements

#### API Security
- **Key Management**: Secure storage and handling of API keys
- **Access Control**: Enhanced role-based access control
- **Rate Limiting**: Protection against API abuse
- **Audit Logging**: Comprehensive audit trails for admin actions

### 📚 Documentation Updates

#### New Documentation
- `MIGRATION_GUIDE.md` - Comprehensive migration guide
- Updated `README.md` with new features and setup instructions
- Enhanced API documentation with new endpoints
- Configuration examples and best practices

### 🐛 Bug Fixes

#### Scraper Issues (Legacy)
- Fixed memory leaks in Selenium drivers
- Improved anti-detection measures
- Better error handling for failed scrapes

#### General Fixes
- Fixed timezone handling in analytics
- Improved error messages and user feedback
- Fixed edge cases in sentiment analysis
- Enhanced data validation and sanitization

### ⚠️ Breaking Changes

#### Deprecated Features
- Web scraper-based monitoring (still functional but deprecated)
- Platform-specific scraper configurations
- Browser automation dependencies (optional)

#### API Changes
- Some legacy endpoints marked as deprecated
- New required fields in user configuration
- Enhanced response formats with additional metadata

### 🔮 Future Deprecations

#### Planned Removals (v3.0.0)
- Legacy web scraper functionality
- Platform-specific scraper endpoints
- Browser automation dependencies
- Old analytics calculation methods

---

## [1.0.0] - 2023-12-01 - Initial Release

### 🚀 Initial Features

#### Core Monitoring
- Web scraper-based monitoring for ChatGPT, Claude, and Perplexity
- Brand mention detection and sentiment analysis
- Competitive analysis and benchmarking
- Automated query generation

#### Analytics & Scoring
- Visibility scoring (0-100)
- Sentiment scoring (-100 to +100)
- Authority scoring (0-100)
- Share of voice calculations

#### Dashboard & Reporting
- Streamlit-based interactive dashboard
- Real-time monitoring and alerts
- Automated weekly and monthly reports
- Export capabilities (CSV, PDF, JSON)

#### API & Integration
- FastAPI-based REST API
- JWT authentication
- User and brand management
- Webhook notifications

#### Technical Foundation
- SQLAlchemy ORM with SQLite database
- Selenium-based web scraping
- Advanced NLP with spaCy and TextBlob
- APScheduler for automated tasks
- Docker containerization

---

## Version History

- **v2.0.0** - OpenRouter API Integration (Current)
- **v1.0.0** - Initial Release with Web Scrapers

## Upgrade Instructions

### From v1.0.0 to v2.0.0

1. **Backup your data**:
   ```bash
   cp data/brand_monitor.db data/brand_monitor_backup.db
   ```

2. **Update dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Update configuration**:
   ```bash
   cp .env.example .env
   # Add your OpenRouter API key and other new settings
   ```

4. **Run migration**:
   ```bash
   python -m app.database.migrations
   ```

5. **Test the upgrade**:
   - Verify existing data is preserved
   - Test OpenRouter API connection
   - Configure new features as needed

For detailed upgrade instructions, see `MIGRATION_GUIDE.md`.
