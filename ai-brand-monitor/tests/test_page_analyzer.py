import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
import httpx
from bs4 import BeautifulSoup

from app.analyzers.page_analyzer import PageAnalyzer
from app.database import models


class TestPageAnalyzer:
    """Test suite for Page Analyzer"""
    
    @pytest.fixture
    def analyzer(self):
        """Create test analyzer instance"""
        return PageAnalyzer(api_key="test-api-key")
    
    @pytest.fixture
    def mock_website_content(self):
        """Mock website HTML content"""
        return """
        <html>
        <head>
            <title>TechCorp - Leading Software Solutions</title>
        </head>
        <body>
            <h1>Welcome to TechCorp</h1>
            <p>We are a leading provider of enterprise software solutions, 
               specializing in cloud computing, data analytics, and AI technologies.</p>
            <h2>Our Products</h2>
            <ul>
                <li>CloudPlatform Pro - Enterprise cloud management</li>
                <li>DataInsight Analytics - Business intelligence platform</li>
                <li>AI Assistant Suite - Artificial intelligence tools</li>
            </ul>
            <h2>Why Choose TechCorp?</h2>
            <p>Unlike competitors like MegaCorp and DataSoft, we focus on 
               innovation and customer success.</p>
            <footer>Contact us for enterprise solutions</footer>
        </body>
        </html>
        """
    
    @pytest.fixture
    def mock_ai_analysis_response(self):
        """Mock AI analysis response"""
        return {
            "success": True,
            "response": """{
                "business_description": "TechCorp is a leading provider of enterprise software solutions specializing in cloud computing, data analytics, and AI technologies.",
                "industry_classification": "Enterprise Software",
                "extracted_keywords": ["cloud computing", "data analytics", "AI technologies", "enterprise software", "business intelligence"],
                "identified_products": ["CloudPlatform Pro", "DataInsight Analytics", "AI Assistant Suite"],
                "identified_competitors": ["MegaCorp", "DataSoft"],
                "brand_positioning": "Innovation-focused enterprise software provider",
                "target_audience": "Enterprise businesses seeking cloud and AI solutions",
                "suggested_queries": [
                    "What are the best enterprise cloud management solutions?",
                    "Can you recommend business intelligence platforms?",
                    "Which companies offer AI tools for enterprises?",
                    "What should I know about TechCorp's software solutions?"
                ],
                "confidence_score": 0.92
            }""",
            "cost": 0.005,
            "usage": {"total_tokens": 150}
        }
    
    def test_analyzer_initialization(self):
        """Test analyzer initialization"""
        analyzer = PageAnalyzer("test-key")
        assert analyzer.openrouter_client.api_key == "test-key"
    
    @pytest.mark.asyncio
    async def test_scrape_website_success(self, analyzer, mock_website_content):
        """Test successful website scraping"""
        with patch('httpx.AsyncClient') as mock_client:
            # Setup mock response
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.text = mock_website_content
            mock_response.raise_for_status.return_value = None
            
            mock_client.return_value.__aenter__.return_value.get = AsyncMock(
                return_value=mock_response
            )
            
            content = await analyzer._scrape_website("https://example.com")
            
            assert content is not None
            assert "TechCorp" in content
            assert "enterprise software solutions" in content
            assert "CloudPlatform Pro" in content
            # Should not contain HTML tags
            assert "<html>" not in content
            assert "<script>" not in content
    
    @pytest.mark.asyncio
    async def test_scrape_website_failure(self, analyzer):
        """Test website scraping failure"""
        with patch('httpx.AsyncClient') as mock_client:
            mock_client.return_value.__aenter__.return_value.get = AsyncMock(
                side_effect=httpx.ConnectError("Connection failed")
            )
            
            content = await analyzer._scrape_website("https://example.com")
            
            assert content is None
    
    @pytest.mark.asyncio
    async def test_scrape_website_long_content(self, analyzer):
        """Test website scraping with long content (should be truncated)"""
        long_content = "<html><body>" + "A" * 10000 + "</body></html>"
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.text = long_content
            mock_response.raise_for_status.return_value = None
            
            mock_client.return_value.__aenter__.return_value.get = AsyncMock(
                return_value=mock_response
            )
            
            content = await analyzer._scrape_website("https://example.com")
            
            assert content is not None
            assert len(content) <= 8003  # 8000 + "..."
            assert content.endswith("...")
    
    @pytest.mark.asyncio
    async def test_analyze_content_with_ai_success(self, analyzer, mock_ai_analysis_response):
        """Test successful AI content analysis"""
        content = "TechCorp is a leading provider of enterprise software solutions."
        
        with patch.object(analyzer.openrouter_client, 'send_query', new_callable=AsyncMock) as mock_send:
            mock_send.return_value = mock_ai_analysis_response
            
            result = await analyzer._analyze_content_with_ai(
                content, "TechCorp", "https://example.com", "openai/gpt-3.5-turbo"
            )
            
            assert "error" not in result
            assert result["business_description"] == "TechCorp is a leading provider of enterprise software solutions specializing in cloud computing, data analytics, and AI technologies."
            assert result["industry_classification"] == "Enterprise Software"
            assert "cloud computing" in result["extracted_keywords"]
            assert "CloudPlatform Pro" in result["identified_products"]
            assert "MegaCorp" in result["identified_competitors"]
            assert len(result["suggested_queries"]) == 4
            assert result["confidence_score"] == 0.92
            assert result["analysis_model"] == "openai/gpt-3.5-turbo"
            assert result["url"] == "https://example.com"
    
    @pytest.mark.asyncio
    async def test_analyze_content_with_ai_failure(self, analyzer):
        """Test AI content analysis failure"""
        content = "Test content"
        
        with patch.object(analyzer.openrouter_client, 'send_query', new_callable=AsyncMock) as mock_send:
            mock_send.return_value = {
                "success": False,
                "error": "API key invalid"
            }
            
            result = await analyzer._analyze_content_with_ai(
                content, "TestBrand", "https://example.com", "openai/gpt-3.5-turbo"
            )
            
            assert "error" in result
            assert "API key invalid" in result["error"]
    
    @pytest.mark.asyncio
    async def test_analyze_content_invalid_json(self, analyzer):
        """Test AI content analysis with invalid JSON response"""
        content = "Test content"
        
        with patch.object(analyzer.openrouter_client, 'send_query', new_callable=AsyncMock) as mock_send:
            mock_send.return_value = {
                "success": True,
                "response": "This is not valid JSON content"
            }
            
            result = await analyzer._analyze_content_with_ai(
                content, "TestBrand", "https://example.com", "openai/gpt-3.5-turbo"
            )
            
            assert "error" in result
            assert "Invalid AI response format" in result["error"]
    
    def test_clean_analysis_result(self, analyzer):
        """Test analysis result cleaning and validation"""
        raw_result = {
            "business_description": "A" * 600,  # Too long
            "industry_classification": "Technology",
            "extracted_keywords": ["keyword1", "keyword2", "a", "", "keyword3"],  # Mixed valid/invalid
            "identified_products": ["Product A", "Product B"],
            "identified_competitors": ["Competitor A", "TestBrand", "Competitor B"],  # Includes brand name
            "brand_positioning": "Great positioning",
            "target_audience": "Enterprise customers",
            "suggested_queries": ["Query 1", "Query 2", "Short", ""],  # Mixed lengths
            "confidence_score": 1.5  # Invalid score
        }
        
        cleaned = analyzer._clean_analysis_result(raw_result, "TestBrand")
        
        # Check length limits
        assert len(cleaned["business_description"]) <= 500
        assert len(cleaned["industry_classification"]) <= 100
        
        # Check keyword cleaning
        assert "keyword1" in cleaned["extracted_keywords"]
        assert "keyword2" in cleaned["extracted_keywords"]
        assert "keyword3" in cleaned["extracted_keywords"]
        assert "a" not in cleaned["extracted_keywords"]  # Too short
        assert "" not in cleaned["extracted_keywords"]  # Empty
        
        # Check competitor cleaning (should exclude brand name)
        assert "Competitor A" in cleaned["identified_competitors"]
        assert "Competitor B" in cleaned["identified_competitors"]
        assert "TestBrand" not in cleaned["identified_competitors"]
        
        # Check query cleaning
        assert "Query 1" in cleaned["suggested_queries"]
        assert "Query 2" in cleaned["suggested_queries"]
        assert "Short" not in cleaned["suggested_queries"]  # Too short
        assert "" not in cleaned["suggested_queries"]  # Empty
        
        # Check confidence score normalization
        assert 0 <= cleaned["confidence_score"] <= 1
        assert cleaned["confidence_score"] == 0.5  # Should be normalized to default
    
    @pytest.mark.asyncio
    async def test_analyze_website_full_flow(self, analyzer, mock_website_content, mock_ai_analysis_response):
        """Test complete website analysis flow"""
        with patch.object(analyzer, '_scrape_website', new_callable=AsyncMock) as mock_scrape:
            mock_scrape.return_value = "TechCorp enterprise software solutions cloud computing"
            
            with patch.object(analyzer, '_analyze_content_with_ai', new_callable=AsyncMock) as mock_analyze:
                mock_analyze.return_value = {
                    "business_description": "Enterprise software provider",
                    "industry_classification": "Technology",
                    "extracted_keywords": ["software", "enterprise"],
                    "identified_products": ["Product A"],
                    "identified_competitors": ["Competitor A"],
                    "suggested_queries": ["Query 1", "Query 2"],
                    "confidence_score": 0.9
                }
                
                result = await analyzer.analyze_website(
                    "https://example.com", "TechCorp", "openai/gpt-3.5-turbo"
                )
                
                assert "error" not in result
                assert result["business_description"] == "Enterprise software provider"
                assert result["industry_classification"] == "Technology"
                assert mock_scrape.called
                assert mock_analyze.called
    
    @pytest.mark.asyncio
    async def test_analyze_website_scraping_failure(self, analyzer):
        """Test website analysis when scraping fails"""
        with patch.object(analyzer, '_scrape_website', new_callable=AsyncMock) as mock_scrape:
            mock_scrape.return_value = None
            
            result = await analyzer.analyze_website(
                "https://example.com", "TechCorp", "openai/gpt-3.5-turbo"
            )
            
            assert "error" in result
            assert "Failed to scrape website content" in result["error"]
    
    @pytest.mark.asyncio
    async def test_save_analysis_new(self, analyzer):
        """Test saving new analysis to database"""
        analysis_result = {
            "business_description": "Test description",
            "industry_classification": "Technology",
            "extracted_keywords": ["keyword1", "keyword2"],
            "identified_products": ["Product A"],
            "identified_competitors": ["Competitor A"],
            "suggested_queries": ["Query 1"],
            "brand_positioning": "Great positioning",
            "target_audience": "Enterprises",
            "analysis_model": "openai/gpt-3.5-turbo",
            "confidence_score": 0.9
        }
        
        with patch('app.database.database.get_db_session') as mock_db_session:
            mock_db = MagicMock()
            mock_db_session.return_value.__enter__.return_value = mock_db
            mock_db_session.return_value.__exit__.return_value = None
            
            # Mock no existing analysis
            mock_db.query.return_value.filter.return_value.first.return_value = None
            
            # Mock successful save
            mock_analysis = MagicMock(spec=models.PageAnalysis)
            mock_db.add.return_value = None
            mock_db.commit.return_value = None
            mock_db.refresh.return_value = None
            
            with patch('app.database.models.PageAnalysis', return_value=mock_analysis):
                result = await analyzer.save_analysis(1, "https://example.com", analysis_result)
                
                assert result == mock_analysis
                mock_db.add.assert_called_once()
                mock_db.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_save_analysis_update_existing(self, analyzer):
        """Test updating existing analysis in database"""
        analysis_result = {
            "business_description": "Updated description",
            "extracted_keywords": ["new_keyword"],
            "confidence_score": 0.95
        }
        
        with patch('app.database.database.get_db_session') as mock_db_session:
            mock_db = MagicMock()
            mock_db_session.return_value.__enter__.return_value = mock_db
            
            # Mock existing analysis
            existing_analysis = MagicMock(spec=models.PageAnalysis)
            mock_db.query.return_value.filter.return_value.first.return_value = existing_analysis
            
            result = await analyzer.save_analysis(1, "https://example.com", analysis_result)
            
            assert result == existing_analysis
            assert existing_analysis.business_description == "Updated description"
            assert existing_analysis.extracted_keywords == ["new_keyword"]
            assert existing_analysis.confidence_score == 0.95
            mock_db.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_generate_additional_queries(self, analyzer):
        """Test generating additional monitoring queries"""
        with patch.object(analyzer.openrouter_client, 'send_query', new_callable=AsyncMock) as mock_send:
            mock_send.return_value = {
                "success": True,
                "response": """1. What are the best enterprise software solutions?
2. Can you recommend cloud computing platforms?
3. Which companies offer data analytics tools?
4. What should I know about AI technologies for business?
5. How do I choose the right business intelligence platform?"""
            }
            
            queries = await analyzer.generate_additional_queries(
                brand_name="TechCorp",
                industry="Technology",
                keywords=["software", "cloud"],
                competitors=["MegaCorp"],
                query_count=5
            )
            
            assert len(queries) == 5
            assert "What are the best enterprise software solutions?" in queries
            assert "Can you recommend cloud computing platforms?" in queries
    
    @pytest.mark.asyncio
    async def test_generate_additional_queries_failure(self, analyzer):
        """Test handling failure in query generation"""
        with patch.object(analyzer.openrouter_client, 'send_query', new_callable=AsyncMock) as mock_send:
            mock_send.return_value = {
                "success": False,
                "error": "API error"
            }
            
            queries = await analyzer.generate_additional_queries(
                brand_name="TechCorp",
                industry="Technology",
                keywords=[],
                competitors=[],
                query_count=5
            )
            
            assert len(queries) == 0
    
    @pytest.mark.asyncio
    async def test_generate_additional_queries_parsing(self, analyzer):
        """Test parsing of generated queries with various formats"""
        with patch.object(analyzer.openrouter_client, 'send_query', new_callable=AsyncMock) as mock_send:
            mock_send.return_value = {
                "success": True,
                "response": """Here are some queries:
1. First query with question?
2. Second query with question?
- Third query with bullet point?
• Fourth query with bullet?
Not a question without question mark
5. Fifth query with question?"""
            }
            
            queries = await analyzer.generate_additional_queries(
                brand_name="TechCorp",
                industry="Technology",
                keywords=[],
                competitors=[],
                query_count=10
            )
            
            # Should only include lines with question marks
            assert len(queries) == 5
            assert all("?" in query for query in queries)
            assert "First query with question?" in queries
            assert "Not a question without question mark" not in queries
