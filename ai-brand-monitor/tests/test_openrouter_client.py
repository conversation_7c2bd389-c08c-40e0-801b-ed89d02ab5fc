import pytest
import async<PERSON>
from unittest.mock import AsyncMock, patch, MagicMock
import httpx

from app.clients.openrouter_client import OpenRouterClient
from app.config import config


class TestOpenRouterClient:
    """Test suite for OpenRouter API client"""
    
    @pytest.fixture
    def client(self):
        """Create test client instance"""
        return OpenRouterClient(api_key="test-api-key")
    
    @pytest.fixture
    def mock_response_data(self):
        """Mock successful API response"""
        return {
            "choices": [
                {
                    "message": {
                        "content": "This is a test response from the AI model."
                    }
                }
            ],
            "usage": {
                "prompt_tokens": 10,
                "completion_tokens": 15,
                "total_tokens": 25
            }
        }
    
    def test_client_initialization(self):
        """Test client initialization with API key"""
        client = OpenRouterClient("test-key")
        assert client.api_key == "test-key"
        assert client.base_url == "https://openrouter.ai/api/v1"
        assert "Authorization" in client.headers
        assert client.headers["Authorization"] == "Bearer test-key"
    
    def test_client_initialization_default_key(self):
        """Test client initialization with default config key"""
        with patch.object(config, 'OPENROUTER_API_KEY', 'config-key'):
            client = OpenRouterClient()
            assert client.api_key == 'config-key'
    
    def test_available_models_structure(self, client):
        """Test available models structure"""
        models = client.available_models
        assert isinstance(models, dict)
        assert "openai/gpt-3.5-turbo" in models
        assert "anthropic/claude-3-haiku" in models
        
        # Check model structure
        gpt_model = models["openai/gpt-3.5-turbo"]
        assert "name" in gpt_model
        assert "provider" in gpt_model
        assert "context_length" in gpt_model
        assert "cost_per_1k_tokens" in gpt_model
    
    @pytest.mark.asyncio
    async def test_send_query_success(self, client, mock_response_data):
        """Test successful query sending"""
        with patch('httpx.AsyncClient') as mock_client:
            # Setup mock response
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = mock_response_data
            mock_response.raise_for_status.return_value = None
            
            mock_client.return_value.__aenter__.return_value.post = AsyncMock(
                return_value=mock_response
            )
            
            # Test query
            result = await client.send_query(
                query="Test query",
                model="openai/gpt-3.5-turbo"
            )
            
            # Assertions
            assert result["success"] is True
            assert result["model"] == "openai/gpt-3.5-turbo"
            assert result["query"] == "Test query"
            assert result["response"] == "This is a test response from the AI model."
            assert result["usage"]["total_tokens"] == 25
            assert result["cost"] > 0  # Should calculate cost
    
    @pytest.mark.asyncio
    async def test_send_query_with_system_prompt(self, client, mock_response_data):
        """Test query with system prompt"""
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = mock_response_data
            mock_response.raise_for_status.return_value = None
            
            mock_client.return_value.__aenter__.return_value.post = AsyncMock(
                return_value=mock_response
            )
            
            result = await client.send_query(
                query="Test query",
                model="openai/gpt-3.5-turbo",
                system_prompt="You are a helpful assistant."
            )
            
            # Check that post was called with correct payload
            call_args = mock_client.return_value.__aenter__.return_value.post.call_args
            payload = call_args[1]['json']
            
            assert len(payload['messages']) == 2
            assert payload['messages'][0]['role'] == 'system'
            assert payload['messages'][0]['content'] == "You are a helpful assistant."
            assert payload['messages'][1]['role'] == 'user'
            assert payload['messages'][1]['content'] == "Test query"
    
    @pytest.mark.asyncio
    async def test_send_query_http_error(self, client):
        """Test handling of HTTP errors"""
        with patch('httpx.AsyncClient') as mock_client:
            # Setup mock error response
            mock_response = MagicMock()
            mock_response.status_code = 400
            mock_response.text = "Bad Request"
            
            mock_client.return_value.__aenter__.return_value.post = AsyncMock(
                side_effect=httpx.HTTPStatusError(
                    "Bad Request", 
                    request=MagicMock(), 
                    response=mock_response
                )
            )
            
            result = await client.send_query("Test query")
            
            assert result["success"] is False
            assert "HTTP 400" in result["error"]
            assert result["response"] is None
            assert result["cost"] == 0.0
    
    @pytest.mark.asyncio
    async def test_send_query_network_error(self, client):
        """Test handling of network errors"""
        with patch('httpx.AsyncClient') as mock_client:
            mock_client.return_value.__aenter__.return_value.post = AsyncMock(
                side_effect=httpx.ConnectError("Connection failed")
            )
            
            result = await client.send_query("Test query")
            
            assert result["success"] is False
            assert "Connection failed" in result["error"]
            assert result["response"] is None
    
    @pytest.mark.asyncio
    async def test_send_batch_queries(self, client, mock_response_data):
        """Test sending multiple queries in batch"""
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = mock_response_data
            mock_response.raise_for_status.return_value = None
            
            mock_client.return_value.__aenter__.return_value.post = AsyncMock(
                return_value=mock_response
            )
            
            queries = ["Query 1", "Query 2", "Query 3"]
            results = await client.send_batch_queries(
                queries=queries,
                delay_between_requests=0.1  # Short delay for testing
            )
            
            assert len(results) == 3
            for i, result in enumerate(results):
                assert result["success"] is True
                assert result["query"] == f"Query {i+1}"
    
    def test_calculate_cost(self, client):
        """Test cost calculation"""
        usage = {
            "prompt_tokens": 100,
            "completion_tokens": 50,
            "total_tokens": 150
        }
        
        # Test with known model
        cost = client._calculate_cost("openai/gpt-3.5-turbo", usage)
        expected_cost = (100/1000 * 0.0015) + (50/1000 * 0.002)
        assert cost == expected_cost
        
        # Test with unknown model
        cost = client._calculate_cost("unknown/model", usage)
        assert cost == 0.0
    
    @pytest.mark.asyncio
    async def test_test_connection_success(self, client):
        """Test successful connection test"""
        with patch.object(client, 'send_query') as mock_send:
            mock_send.return_value = {
                "success": True,
                "response": "Connection successful",
                "response_time": 1.5,
                "cost": 0.001
            }
            
            result = await client.test_connection()
            
            assert result["status"] == "success"
            assert "successful" in result["message"]
            assert result["response_time"] == 1.5
            assert result["cost"] == 0.001
    
    @pytest.mark.asyncio
    async def test_test_connection_failure(self, client):
        """Test failed connection test"""
        with patch.object(client, 'send_query') as mock_send:
            mock_send.return_value = {
                "success": False,
                "error": "API key invalid"
            }
            
            result = await client.test_connection()
            
            assert result["status"] == "error"
            assert "API key invalid" in result["message"]
    
    @pytest.mark.asyncio
    async def test_get_available_models(self, client):
        """Test fetching available models from API"""
        mock_models_data = {
            "data": [
                {
                    "id": "openai/gpt-4",
                    "name": "GPT-4",
                    "context_length": 8192,
                    "pricing": {"prompt": 0.00003, "completion": 0.00006}
                }
            ]
        }
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = mock_models_data
            mock_response.raise_for_status.return_value = None
            
            mock_client.return_value.__aenter__.return_value.get = AsyncMock(
                return_value=mock_response
            )
            
            models = await client.get_available_models()
            
            assert len(models) == 1
            assert models[0]["id"] == "openai/gpt-4"
            assert models[0]["name"] == "GPT-4"
    
    def test_get_model_info(self, client):
        """Test getting model information"""
        model_info = client.get_model_info("openai/gpt-3.5-turbo")
        
        assert model_info is not None
        assert model_info["name"] == "GPT-3.5 Turbo"
        assert model_info["provider"] == "OpenAI"
        
        # Test unknown model
        unknown_info = client.get_model_info("unknown/model")
        assert unknown_info is None
    
    def test_get_supported_models(self, client):
        """Test getting list of supported models"""
        models = client.get_supported_models()
        
        assert isinstance(models, list)
        assert "openai/gpt-3.5-turbo" in models
        assert "anthropic/claude-3-haiku" in models
        assert len(models) > 0
    
    @pytest.mark.asyncio
    async def test_rate_limiting(self, client):
        """Test rate limiting functionality"""
        # This test verifies that the throttler is working
        # In a real scenario, this would test actual rate limiting
        with patch.object(client, 'throttler') as mock_throttler:
            mock_throttler.__aenter__ = AsyncMock()
            mock_throttler.__aexit__ = AsyncMock()

            with patch('httpx.AsyncClient') as mock_client:
                mock_response = MagicMock()
                mock_response.status_code = 200
                mock_response.json.return_value = {
                    "choices": [{"message": {"content": "test"}}],
                    "usage": {"total_tokens": 10}
                }
                mock_response.raise_for_status.return_value = None

                mock_client.return_value.__aenter__.return_value.post = AsyncMock(
                    return_value=mock_response
                )

                await client.send_query("Test query")

                # Verify throttler was used
                mock_throttler.__aenter__.assert_called_once()
    
    def test_headers_configuration(self, client):
        """Test that headers are properly configured"""
        headers = client.headers
        
        assert headers["Authorization"] == "Bearer test-api-key"
        assert headers["Content-Type"] == "application/json"
        assert headers["HTTP-Referer"] == config.APP_URL
        assert headers["X-Title"] == "AI Brand Monitor"


@pytest.mark.asyncio
async def test_integration_with_real_api():
    """Integration test with real API (requires valid API key)"""
    # This test should only run if a real API key is available
    api_key = config.OPENROUTER_API_KEY
    if not api_key or api_key == "your-openrouter-api-key":
        pytest.skip("No valid OpenRouter API key available for integration test")
    
    client = OpenRouterClient(api_key)
    
    # Test connection
    result = await client.test_connection()
    assert result["status"] == "success"
    
    # Test simple query
    result = await client.send_query(
        query="Hello, this is a test. Please respond with 'Test successful'.",
        model="openai/gpt-3.5-turbo",
        max_tokens=50
    )
    
    assert result["success"] is True
    assert result["response"] is not None
    assert len(result["response"]) > 0
    assert result["cost"] > 0
