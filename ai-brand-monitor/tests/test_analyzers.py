import pytest
from app.analyzers.sentiment import SentimentAnalyzer
from app.analyzers.nlp_analyzer import NLPAnalyzer
from app.analyzers.scoring import ScoringEngine
from app.analyzers.competitor import CompetitorAnalyzer

class TestSentimentAnalyzer:
    """Test sentiment analysis functionality"""
    
    def setup_method(self):
        self.analyzer = SentimentAnalyzer()
    
    def test_positive_sentiment(self):
        """Test positive sentiment detection"""
        text = "Apple makes excellent products with amazing quality and innovative design."
        result = self.analyzer.analyze_sentiment(text, "Apple")
        
        assert result['brand_sentiment_score'] > 0
        assert result['brand_sentiment_label'] == 'positive'
        assert len(result['sentiment_indicators']) > 0
    
    def test_negative_sentiment(self):
        """Test negative sentiment detection"""
        text = "Apple products are terrible, overpriced and have poor quality."
        result = self.analyzer.analyze_sentiment(text, "Apple")
        
        assert result['brand_sentiment_score'] < 0
        assert result['brand_sentiment_label'] == 'negative'
        assert len(result['sentiment_indicators']) > 0
    
    def test_neutral_sentiment(self):
        """Test neutral sentiment detection"""
        text = "Apple is a technology company that makes phones and computers."
        result = self.analyzer.analyze_sentiment(text, "Apple")
        
        assert abs(result['brand_sentiment_score']) < 0.3
        assert result['brand_sentiment_label'] == 'neutral'
    
    def test_no_brand_mention(self):
        """Test sentiment when brand is not mentioned"""
        text = "This is a text without any brand mentions."
        result = self.analyzer.analyze_sentiment(text, "Apple")
        
        assert result['brand_sentiment_score'] == 0.0
        assert result['brand_sentiment_confidence'] == 0.0

class TestNLPAnalyzer:
    """Test NLP analysis functionality"""
    
    def setup_method(self):
        self.analyzer = NLPAnalyzer()
    
    def test_brand_mention_extraction(self):
        """Test brand mention extraction"""
        text = "Apple makes great phones. I love Apple products. Apple is innovative."
        mentions = self.analyzer.extract_brand_mentions(text, "Apple")
        
        assert len(mentions) == 3
        for mention in mentions:
            assert mention['brand'] == 'Apple'
            assert mention['is_target_brand'] == True
    
    def test_competitor_mention_extraction(self):
        """Test competitor mention extraction"""
        text = "Apple and Samsung are both good phone makers. Google also makes phones."
        mentions = self.analyzer.extract_brand_mentions(text, "Apple", ["Samsung", "Google"])
        
        apple_mentions = [m for m in mentions if m['brand'] == 'Apple']
        samsung_mentions = [m for m in mentions if m['brand'] == 'Samsung']
        google_mentions = [m for m in mentions if m['brand'] == 'Google']
        
        assert len(apple_mentions) == 1
        assert len(samsung_mentions) == 1
        assert len(google_mentions) == 1
    
    def test_context_extraction(self):
        """Test context extraction around mentions"""
        text = "In the smartphone market, Apple creates innovative devices with premium quality."
        mentions = self.analyzer.extract_brand_mentions(text, "Apple")
        
        assert len(mentions) == 1
        mention = mentions[0]
        assert 'smartphone' in mention['context'].lower()
        assert 'innovative' in mention['context'].lower()
    
    def test_keyword_extraction(self):
        """Test keyword extraction"""
        text = "Apple iPhone is an innovative smartphone with excellent camera quality and premium design."
        keywords = self.analyzer.extract_keywords(text, top_n=5)
        
        assert len(keywords) <= 5
        # Keywords should be tuples of (word, count)
        for keyword, count in keywords:
            assert isinstance(keyword, str)
            assert isinstance(count, int)
            assert count > 0

class TestScoringEngine:
    """Test scoring engine functionality"""
    
    def setup_method(self):
        self.engine = ScoringEngine()
    
    def test_visibility_score_calculation(self):
        """Test visibility score calculation"""
        mentions = [
            {'position_in_response': 10, 'mention_type': 'recommendation', 'is_primary_focus': True, 'platform': 'chatgpt'},
            {'position_in_response': 50, 'mention_type': 'comparison', 'is_primary_focus': False, 'platform': 'claude'},
            {'position_in_response': 100, 'mention_type': 'factual', 'is_primary_focus': True, 'platform': 'perplexity'}
        ]
        
        score = self.engine.calculate_visibility_score(mentions, total_queries=10)
        
        assert 0 <= score <= 100
        assert isinstance(score, float)
    
    def test_sentiment_score_calculation(self):
        """Test sentiment score calculation"""
        mentions = [
            {'sentiment_score': 0.8, 'confidence_score': 0.9},
            {'sentiment_score': 0.6, 'confidence_score': 0.7},
            {'sentiment_score': -0.2, 'confidence_score': 0.8}
        ]
        
        score = self.engine.calculate_sentiment_score(mentions)
        
        assert -100 <= score <= 100
        assert isinstance(score, float)
    
    def test_authority_score_calculation(self):
        """Test authority score calculation"""
        mentions = [
            {'mention_type': 'recommendation', 'context': 'Apple is the leader in smartphones', 'comparison_result': 'wins'},
            {'mention_type': 'factual', 'context': 'Apple is a trusted brand', 'comparison_result': 'neutral'},
            {'mention_type': 'comparison', 'context': 'Apple is the best choice', 'comparison_result': 'wins'}
        ]
        
        responses = []  # Mock responses
        score = self.engine.calculate_authority_score(mentions, responses)
        
        assert 0 <= score <= 100
        assert isinstance(score, float)
    
    def test_share_of_voice_calculation(self):
        """Test share of voice calculation"""
        brand_mentions = 15
        competitor_mentions = {'Samsung': 10, 'Google': 5, 'Huawei': 3}
        
        share = self.engine.calculate_share_of_voice(brand_mentions, competitor_mentions)
        
        assert 0 <= share <= 100
        assert isinstance(share, float)
        # Should be 15/(15+10+5+3) * 100 = 45.45%
        assert abs(share - 45.45) < 0.1

class TestCompetitorAnalyzer:
    """Test competitor analysis functionality"""
    
    def setup_method(self):
        self.analyzer = CompetitorAnalyzer()
    
    def test_competitor_mention_analysis(self):
        """Test competitor mention analysis"""
        responses = [
            {'response': 'Apple and Samsung are both great phone makers. Apple has better design.'},
            {'response': 'Samsung phones have good cameras. Apple phones are more expensive.'},
            {'response': 'Google makes Android, which Samsung uses. Apple makes iOS.'}
        ]
        
        result = self.analyzer.analyze_competitor_mentions(responses, "Apple", ["Samsung", "Google"])
        
        assert 'Samsung' in result
        assert 'Google' in result
        assert result['Samsung']['mention_count'] > 0
        assert result['Google']['mention_count'] > 0
    
    def test_competitive_landscape_calculation(self):
        """Test competitive landscape calculation"""
        competitor_data = {
            'Samsung': {'mention_count': 10, 'co_mentions': 5, 'comparison_wins': 2, 'comparison_losses': 3},
            'Google': {'mention_count': 5, 'co_mentions': 2, 'comparison_wins': 1, 'comparison_losses': 1},
            'Huawei': {'mention_count': 3, 'co_mentions': 1, 'comparison_wins': 0, 'comparison_losses': 2}
        }
        
        landscape = self.analyzer.calculate_competitive_landscape(competitor_data)
        
        assert 'total_competitor_mentions' in landscape
        assert 'top_competitors' in landscape
        assert 'competitive_intensity' in landscape
        assert 'market_concentration' in landscape
        
        assert landscape['total_competitor_mentions'] == 18
        assert len(landscape['top_competitors']) <= 3
    
    def test_competitive_advantages_identification(self):
        """Test competitive advantages identification"""
        responses = [
            {'response': 'Apple products have premium quality and innovative design.'},
            {'response': 'Apple offers excellent customer service and reliable products.'},
            {'response': 'Apple is known for its performance and beautiful aesthetic.'}
        ]
        
        advantages = self.analyzer.identify_competitive_advantages(responses, "Apple")
        
        assert isinstance(advantages, list)
        # Should identify quality, innovation, service, performance, design
        expected_advantages = ['quality', 'innovation', 'service', 'performance', 'design']
        for advantage in advantages:
            assert advantage in expected_advantages
