import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import tempfile
import os

from app.main import app
from app.database.database import get_db
from app.database.models import Base

# Create test database
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

@pytest.fixture
def client():
    Base.metadata.create_all(bind=engine)
    with TestClient(app) as c:
        yield c
    Base.metadata.drop_all(bind=engine)

def test_root_endpoint(client):
    """Test root endpoint"""
    response = client.get("/")
    assert response.status_code == 200
    assert "AI Brand Monitor API" in response.json()["message"]

def test_health_check(client):
    """Test health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json()["status"] == "healthy"

def test_user_registration(client):
    """Test user registration"""
    user_data = {
        "email": "<EMAIL>",
        "password": "testpassword",
        "full_name": "Test User"
    }
    response = client.post("/api/v1/register", json=user_data)
    assert response.status_code == 200
    assert response.json()["email"] == user_data["email"]

def test_user_login(client):
    """Test user login"""
    # First register a user
    user_data = {
        "email": "<EMAIL>",
        "password": "testpassword",
        "full_name": "Test User"
    }
    client.post("/api/v1/register", json=user_data)
    
    # Then login
    login_data = {
        "username": "<EMAIL>",
        "password": "testpassword"
    }
    response = client.post("/api/v1/token", data=login_data)
    assert response.status_code == 200
    assert "access_token" in response.json()

def test_create_brand(client):
    """Test brand creation"""
    # Register and login user
    user_data = {
        "email": "<EMAIL>",
        "password": "testpassword",
        "full_name": "Test User"
    }
    client.post("/api/v1/register", json=user_data)
    
    login_response = client.post("/api/v1/token", data={
        "username": "<EMAIL>",
        "password": "testpassword"
    })
    token = login_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    
    # Create brand
    brand_data = {
        "name": "Test Brand",
        "description": "A test brand",
        "industry": "Technology",
        "keywords": ["test", "brand"],
        "competitors": ["Competitor A", "Competitor B"]
    }
    response = client.post("/api/v1/brands", json=brand_data, headers=headers)
    assert response.status_code == 200
    assert response.json()["name"] == brand_data["name"]

def test_list_brands(client):
    """Test listing brands"""
    # Register and login user
    user_data = {
        "email": "<EMAIL>",
        "password": "testpassword",
        "full_name": "Test User"
    }
    client.post("/api/v1/register", json=user_data)
    
    login_response = client.post("/api/v1/token", data={
        "username": "<EMAIL>",
        "password": "testpassword"
    })
    token = login_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    
    # Create a brand first
    brand_data = {
        "name": "Test Brand",
        "description": "A test brand",
        "industry": "Technology",
        "keywords": ["test", "brand"],
        "competitors": ["Competitor A"]
    }
    client.post("/api/v1/brands", json=brand_data, headers=headers)
    
    # List brands
    response = client.get("/api/v1/brands", headers=headers)
    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["name"] == "Test Brand"

def test_unauthorized_access(client):
    """Test unauthorized access to protected endpoints"""
    response = client.get("/api/v1/brands")
    assert response.status_code == 401

def test_invalid_login(client):
    """Test login with invalid credentials"""
    login_data = {
        "username": "<EMAIL>",
        "password": "wrongpassword"
    }
    response = client.post("/api/v1/token", data=login_data)
    assert response.status_code == 401
