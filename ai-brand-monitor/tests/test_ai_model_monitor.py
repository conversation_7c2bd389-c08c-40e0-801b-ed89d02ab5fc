import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime

from app.clients.ai_model_monitor import AIModelMonitor
from app.database import models
from app.clients.openrouter_client import OpenRouterClient


class TestAIModelMonitor:
    """Test suite for AI Model Monitor"""
    
    @pytest.fixture
    def monitor(self):
        """Create test monitor instance"""
        return AIModelMonitor(api_key="test-api-key")
    
    @pytest.fixture
    def mock_brand(self):
        """Create mock brand object"""
        brand = MagicMock(spec=models.Brand)
        brand.id = 1
        brand.name = "Test Brand"
        brand.industry = "Technology"
        brand.keywords = ["software", "technology", "innovation"]
        brand.competitors = ["Competitor A", "Competitor B"]
        return brand
    
    @pytest.fixture
    def mock_ai_model(self):
        """Create mock AI model object"""
        ai_model = MagicMock(spec=models.AIModel)
        ai_model.id = 1
        ai_model.model_id = "openai/gpt-3.5-turbo"
        ai_model.name = "GPT-3.5 Turbo"
        ai_model.is_enabled = True
        ai_model.default_temperature = 0.7
        ai_model.default_max_tokens = 1000
        return ai_model
    
    @pytest.fixture
    def mock_openrouter_response(self):
        """Mock successful OpenRouter API response"""
        return {
            "success": True,
            "model": "openai/gpt-3.5-turbo",
            "query": "Test query",
            "response": "This is a test response mentioning Test Brand as a great solution.",
            "response_time": 1.5,
            "timestamp": datetime.utcnow().isoformat(),
            "usage": {
                "prompt_tokens": 20,
                "completion_tokens": 30,
                "total_tokens": 50
            },
            "cost": 0.001,
            "error": None
        }
    
    def test_monitor_initialization(self):
        """Test monitor initialization"""
        monitor = AIModelMonitor("test-key")
        assert isinstance(monitor.openrouter_client, OpenRouterClient)
        assert monitor.openrouter_client.api_key == "test-key"
    
    @pytest.mark.asyncio
    async def test_monitor_brand_success(self, monitor, mock_brand, mock_ai_model, mock_openrouter_response):
        """Test successful brand monitoring"""
        queries = ["What are the best technology solutions?", "Can you recommend software companies?"]
        models_to_use = ["openai/gpt-3.5-turbo"]
        
        with patch('app.database.database.get_db_session') as mock_db_session:
            # Setup mock database session
            mock_db = MagicMock()
            mock_db_session.return_value.__enter__.return_value = mock_db
            mock_db_session.return_value.__exit__.return_value = None
            
            # Mock AI model query
            mock_db.query.return_value.filter.return_value.first.return_value = mock_ai_model
            
            # Mock OpenRouter client response
            with patch.object(monitor.openrouter_client, 'send_query', new_callable=AsyncMock) as mock_send:
                mock_send.return_value = mock_openrouter_response
                
                # Mock analyzers
                with patch.object(monitor.nlp_analyzer, 'extract_brand_mentions') as mock_extract:
                    mock_extract.return_value = [{
                        "brand": "Test Brand",
                        "text": "Test Brand as a great solution",
                        "context": "This is a test response mentioning Test Brand as a great solution.",
                        "start_pos": 45,
                        "end_pos": 55,
                        "is_target_brand": True
                    }]
                    
                    with patch.object(monitor.sentiment_analyzer, 'analyze_sentiment') as mock_sentiment:
                        mock_sentiment.return_value = {
                            "brand_sentiment_score": 0.8,
                            "brand_sentiment_confidence": 0.9
                        }
                        
                        with patch.object(monitor.nlp_analyzer, 'analyze_mention_context') as mock_context:
                            mock_context.return_value = {"mention_type": "recommendation"}
                            
                            # Run monitoring
                            result = await monitor.monitor_brand(
                                brand=mock_brand,
                                queries=queries,
                                models_to_use=models_to_use,
                                user_id=1
                            )
                            
                            # Assertions
                            assert result["brand_id"] == 1
                            assert result["brand_name"] == "Test Brand"
                            assert result["total_queries"] == 2
                            assert result["total_models"] == 1
                            assert len(result["responses"]) == 2
                            assert len(result["mentions"]) == 2  # One mention per query
                            assert "analytics" in result
    
    @pytest.mark.asyncio
    async def test_monitor_brand_with_user_limits(self, monitor, mock_brand, mock_ai_model):
        """Test monitoring with user limit checking"""
        queries = ["Test query"]
        models_to_use = ["openai/gpt-3.5-turbo"]
        
        with patch('app.database.database.get_db_session') as mock_db_session:
            mock_db = MagicMock()
            mock_db_session.return_value.__enter__.return_value = mock_db
            
            # Mock AI model query
            mock_db.query.return_value.filter.return_value.first.return_value = mock_ai_model
            
            # Mock user limits check - should fail
            with patch.object(monitor, '_check_user_limits', new_callable=AsyncMock) as mock_check:
                mock_check.return_value = False
                
                result = await monitor.monitor_brand(
                    brand=mock_brand,
                    queries=queries,
                    models_to_use=models_to_use,
                    user_id=1
                )
                
                # Should skip monitoring due to limits
                assert len(result["responses"]) == 0
                assert len(result["mentions"]) == 0
    
    @pytest.mark.asyncio
    async def test_monitor_brand_with_disabled_model(self, monitor, mock_brand):
        """Test monitoring with disabled AI model"""
        queries = ["Test query"]
        models_to_use = ["disabled/model"]
        
        with patch('app.database.database.get_db_session') as mock_db_session:
            mock_db = MagicMock()
            mock_db_session.return_value.__enter__.return_value = mock_db
            
            # Mock AI model query - return None (model not found/disabled)
            mock_db.query.return_value.filter.return_value.first.return_value = None
            
            result = await monitor.monitor_brand(
                brand=mock_brand,
                queries=queries,
                models_to_use=models_to_use,
                user_id=1
            )
            
            # Should skip disabled model
            assert len(result["responses"]) == 0
            assert len(result["mentions"]) == 0
    
    def test_create_monitoring_prompt(self, monitor, mock_brand):
        """Test monitoring prompt creation"""
        prompt = monitor._create_monitoring_prompt(mock_brand)
        
        assert "Test Brand" in prompt
        assert "Technology" in prompt
        assert "Competitor A" in prompt
        assert "Competitor B" in prompt
        assert "software" in prompt
        assert "technology" in prompt
        assert "innovation" in prompt
    
    def test_create_monitoring_prompt_minimal(self, monitor):
        """Test monitoring prompt with minimal brand data"""
        brand = MagicMock(spec=models.Brand)
        brand.name = "Minimal Brand"
        brand.industry = None
        brand.keywords = None
        brand.competitors = None
        
        prompt = monitor._create_monitoring_prompt(brand)
        
        assert "Minimal Brand" in prompt
        assert "relevant industry" in prompt  # fallback text
    
    @pytest.mark.asyncio
    async def test_check_user_limits_success(self, monitor):
        """Test successful user limits check"""
        with patch('app.database.database.get_db_session') as mock_db_session:
            mock_db = MagicMock()
            mock_db_session.return_value.__enter__.return_value = mock_db
            
            # Mock user query
            mock_user = MagicMock()
            mock_user.daily_api_limit = 100
            mock_db.query.return_value.filter.return_value.first.return_value = mock_user
            
            # Mock usage query - return low usage
            mock_db.query.return_value.filter.return_value.count.return_value = 10
            
            result = await monitor._check_user_limits(1, 1, 5, mock_db)
            
            assert result is True
    
    @pytest.mark.asyncio
    async def test_check_user_limits_exceeded(self, monitor):
        """Test user limits exceeded"""
        with patch('app.database.database.get_db_session') as mock_db_session:
            mock_db = MagicMock()
            mock_db_session.return_value.__enter__.return_value = mock_db
            
            # Mock user query
            mock_user = MagicMock()
            mock_user.daily_api_limit = 50
            mock_db.query.return_value.filter.return_value.first.return_value = mock_user
            
            # Mock usage query - return high usage
            mock_db.query.return_value.filter.return_value.count.return_value = 48
            
            result = await monitor._check_user_limits(1, 1, 5, mock_db)  # Would exceed limit
            
            assert result is False
    
    @pytest.mark.asyncio
    async def test_analyze_response_with_mentions(self, monitor, mock_brand):
        """Test response analysis with brand mentions"""
        # Create mock objects
        ai_response = MagicMock(spec=models.AIResponse)
        ai_response.response_text = "I recommend Test Brand as the best solution for your needs."
        ai_response.model_name = "openai/gpt-3.5-turbo"
        
        query = MagicMock(spec=models.Query)
        query.id = 1
        
        mock_db = MagicMock()
        
        # Mock NLP analyzer
        with patch.object(monitor.nlp_analyzer, 'extract_brand_mentions') as mock_extract:
            mock_extract.return_value = [{
                "brand": "Test Brand",
                "text": "Test Brand",
                "context": "I recommend Test Brand as the best solution",
                "start_pos": 12,
                "end_pos": 22,
                "is_target_brand": True
            }]
            
            # Mock sentiment analyzer
            with patch.object(monitor.sentiment_analyzer, 'analyze_sentiment') as mock_sentiment:
                mock_sentiment.return_value = {
                    "brand_sentiment_score": 0.9,
                    "brand_sentiment_confidence": 0.95
                }
                
                # Mock context analyzer
                with patch.object(monitor.nlp_analyzer, 'analyze_mention_context') as mock_context:
                    mock_context.return_value = {"mention_type": "recommendation"}
                    
                    mentions = await monitor._analyze_response(ai_response, mock_brand, query, mock_db)
                    
                    assert len(mentions) == 1
                    mention = mentions[0]
                    assert mention["brand"] == "Test Brand"
                    assert mention["sentiment"] == 0.9
                    assert mention["confidence"] == 0.95
                    assert mention["type"] == "recommendation"
                    assert mention["is_competitor"] is False
    
    @pytest.mark.asyncio
    async def test_analyze_response_no_mentions(self, monitor, mock_brand):
        """Test response analysis with no brand mentions"""
        ai_response = MagicMock(spec=models.AIResponse)
        ai_response.response_text = "This is a generic response with no brand mentions."
        
        query = MagicMock(spec=models.Query)
        mock_db = MagicMock()
        
        with patch.object(monitor.nlp_analyzer, 'extract_brand_mentions') as mock_extract:
            mock_extract.return_value = []  # No mentions found
            
            mentions = await monitor._analyze_response(ai_response, mock_brand, query, mock_db)
            
            assert len(mentions) == 0
    
    @pytest.mark.asyncio
    async def test_calculate_analytics(self, monitor, mock_brand):
        """Test analytics calculation"""
        mentions = [
            {
                "brand": "Test Brand",
                "sentiment": 0.8,
                "confidence": 0.9,
                "type": "recommendation",
                "is_competitor": False,
                "model": "openai/gpt-3.5-turbo"
            },
            {
                "brand": "Test Brand",
                "sentiment": -0.2,
                "confidence": 0.7,
                "type": "comparison",
                "is_competitor": False,
                "model": "anthropic/claude-3-haiku"
            }
        ]
        
        responses = [
            {"model": "openai/gpt-3.5-turbo", "success": True},
            {"model": "anthropic/claude-3-haiku", "success": True}
        ]
        
        with patch.object(monitor.scoring_engine, 'calculate_visibility_score') as mock_visibility:
            mock_visibility.return_value = 75.0
            
            with patch.object(monitor.scoring_engine, 'calculate_sentiment_score') as mock_sentiment:
                mock_sentiment.return_value = 0.3
                
                with patch.object(monitor.scoring_engine, 'calculate_authority_score') as mock_authority:
                    mock_authority.return_value = 80.0
                    
                    analytics = await monitor._calculate_analytics(mock_brand, mentions, responses)
                    
                    assert analytics["visibility_score"] == 75.0
                    assert analytics["sentiment_score"] == 0.3
                    assert analytics["authority_score"] == 80.0
                    assert analytics["total_mentions"] == 2
                    assert analytics["positive_mentions"] == 1
                    assert analytics["negative_mentions"] == 1
                    assert analytics["neutral_mentions"] == 0
                    assert "openai/gpt-3.5-turbo" in analytics["models_coverage"]
                    assert "anthropic/claude-3-haiku" in analytics["models_coverage"]
    
    @pytest.mark.asyncio
    async def test_calculate_analytics_no_mentions(self, monitor, mock_brand):
        """Test analytics calculation with no mentions"""
        mentions = []
        responses = [{"model": "openai/gpt-3.5-turbo", "success": True}]
        
        analytics = await monitor._calculate_analytics(mock_brand, mentions, responses)
        
        assert analytics["visibility_score"] == 0.0
        assert analytics["sentiment_score"] == 0.0
        assert analytics["authority_score"] == 0.0
        assert analytics["total_mentions"] == 0
        assert analytics["positive_mentions"] == 0
        assert analytics["negative_mentions"] == 0
        assert analytics["neutral_mentions"] == 0
        assert analytics["models_coverage"] == {}
        assert analytics["mention_types"] == {}
    
    @pytest.mark.asyncio
    async def test_error_handling_in_monitoring(self, monitor, mock_brand, mock_ai_model):
        """Test error handling during monitoring"""
        queries = ["Test query"]
        models_to_use = ["openai/gpt-3.5-turbo"]
        
        with patch('app.database.database.get_db_session') as mock_db_session:
            mock_db = MagicMock()
            mock_db_session.return_value.__enter__.return_value = mock_db
            
            # Mock AI model query
            mock_db.query.return_value.filter.return_value.first.return_value = mock_ai_model
            
            # Mock user limits check
            with patch.object(monitor, '_check_user_limits', new_callable=AsyncMock) as mock_check:
                mock_check.return_value = True
                
                # Mock OpenRouter client to raise exception
                with patch.object(monitor.openrouter_client, 'send_query', new_callable=AsyncMock) as mock_send:
                    mock_send.side_effect = Exception("API Error")
                    
                    result = await monitor.monitor_brand(
                        brand=mock_brand,
                        queries=queries,
                        models_to_use=models_to_use,
                        user_id=1
                    )
                    
                    # Should handle error gracefully
                    assert len(result["errors"]) == 1
                    assert "API Error" in result["errors"][0]["error"]
                    assert result["errors"][0]["query"] == "Test query"
                    assert result["errors"][0]["model"] == "openai/gpt-3.5-turbo"
