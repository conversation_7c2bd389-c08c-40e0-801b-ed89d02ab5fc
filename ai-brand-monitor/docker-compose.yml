version: '3.8'

services:
  ai-brand-monitor:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - SECRET_KEY=your-secret-key-change-in-production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  streamlit-dashboard:
    build: .
    command: streamlit run app/web/dashboard.py --server.port=8501 --server.address=0.0.0.0
    ports:
      - "8501:8501"
    depends_on:
      - ai-brand-monitor
    environment:
      - API_BASE_URL=http://ai-brand-monitor:8000/api/v1
    restart: unless-stopped

volumes:
  data:
  logs:
