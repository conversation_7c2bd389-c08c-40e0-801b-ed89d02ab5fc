#!/usr/bin/env python3
"""
AI Brand Monitor Runner
Simple script to start the application components
"""

import subprocess
import sys
import time
import signal
import os
from pathlib import Path

def start_api_server():
    """Start the FastAPI server"""
    print("🚀 Starting API server...")
    return subprocess.Popen([
        sys.executable, "-m", "app.main"
    ], cwd=Path(__file__).parent)

def start_dashboard():
    """Start the Streamlit dashboard"""
    print("📊 Starting dashboard...")
    return subprocess.Popen([
        "streamlit", "run", "app/web/dashboard.py",
        "--server.port=8501",
        "--server.address=0.0.0.0"
    ], cwd=Path(__file__).parent)

def main():
    """Main runner function"""
    print("🎯 AI Brand Monitor")
    print("=" * 40)
    
    # Check if setup has been run
    if not Path("data").exists() or not Path(".env").exists():
        print("⚠️  Setup not completed. Running setup first...")
        setup_result = subprocess.run([sys.executable, "setup.py"])
        if setup_result.returncode != 0:
            print("❌ Setup failed. Please run setup.py manually.")
            sys.exit(1)
    
    processes = []
    
    try:
        # Start API server
        api_process = start_api_server()
        processes.append(api_process)
        
        # Wait a bit for API to start
        time.sleep(3)
        
        # Start dashboard
        dashboard_process = start_dashboard()
        processes.append(dashboard_process)
        
        print("\n✅ Application started successfully!")
        print("\n🌐 Access URLs:")
        print("   API Server: http://localhost:8000")
        print("   Dashboard:  http://localhost:8501")
        print("   API Docs:   http://localhost:8000/docs")
        print("\n📝 Default admin credentials:")
        print("   Email:    <EMAIL>")
        print("   Password: admin123")
        print("\n⏹️  Press Ctrl+C to stop all services")
        
        # Wait for processes
        while True:
            time.sleep(1)
            # Check if any process has died
            for process in processes:
                if process.poll() is not None:
                    print(f"⚠️  Process {process.pid} has stopped")
                    break
    
    except KeyboardInterrupt:
        print("\n🛑 Stopping services...")
        
        # Terminate all processes
        for process in processes:
            try:
                process.terminate()
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
        
        print("✅ All services stopped")

if __name__ == "__main__":
    main()
