# AI Brand Monitor - Deployment Guide

## Overview

This guide covers deployment of the modernized AI Brand Monitor with OpenRouter API integration. The new architecture is more scalable and cost-effective than the previous scraper-based system.

## Deployment Options

### 1. Docker Deployment (Recommended)

#### Prerequisites
- Docker and Docker Compose
- OpenRouter API key
- Domain name (for production)

#### Quick Start

1. **Clone and configure**:
```bash
git clone <repository-url>
cd ai-brand-monitor
cp .env.example .env
```

2. **Configure environment**:
```bash
# Edit .env file
OPENROUTER_API_KEY=your-openrouter-api-key
SECRET_KEY=your-production-secret-key
DATABASE_URL=sqlite:///./data/brand_monitor.db
API_HOST=0.0.0.0
API_PORT=8000
```

3. **Deploy**:
```bash
docker-compose up -d
```

4. **Initialize database**:
```bash
docker-compose exec app python -m app.database.migrations
```

#### Production Docker Setup

Create `docker-compose.prod.yml`:
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**********************************/brand_monitor
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - SECRET_KEY=${SECRET_KEY}
    depends_on:
      - db
      - redis
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    restart: unless-stopped

  dashboard:
    build: .
    command: streamlit run app/web/dashboard.py --server.port=8501 --server.address=0.0.0.0
    ports:
      - "8501:8501"
    environment:
      - API_BASE_URL=http://app:8000
    depends_on:
      - app
    restart: unless-stopped

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=brand_monitor
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
      - dashboard
    restart: unless-stopped

volumes:
  postgres_data:
```

### 2. Manual Deployment

#### System Requirements
- Ubuntu 20.04+ or CentOS 8+
- Python 3.11+
- PostgreSQL 13+ (for production)
- Redis (optional, for caching)
- Nginx (for reverse proxy)

#### Installation Steps

1. **System setup**:
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Python and dependencies
sudo apt install python3.11 python3.11-venv python3.11-dev
sudo apt install postgresql postgresql-contrib redis-server nginx

# Create application user
sudo useradd -m -s /bin/bash aimonitor
sudo su - aimonitor
```

2. **Application setup**:
```bash
# Clone repository
git clone <repository-url>
cd ai-brand-monitor

# Create virtual environment
python3.11 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
python -m spacy download en_core_web_sm
```

3. **Database setup**:
```bash
# Create PostgreSQL database
sudo -u postgres createdb brand_monitor
sudo -u postgres createuser aimonitor
sudo -u postgres psql -c "ALTER USER aimonitor WITH PASSWORD 'secure_password';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE brand_monitor TO aimonitor;"
```

4. **Configuration**:
```bash
# Configure environment
cp .env.example .env
# Edit .env with production settings

# Initialize database
python -m app.database.migrations
```

5. **Service setup**:

Create `/etc/systemd/system/ai-brand-monitor.service`:
```ini
[Unit]
Description=AI Brand Monitor API
After=network.target postgresql.service

[Service]
Type=simple
User=aimonitor
WorkingDirectory=/home/<USER>/ai-brand-monitor
Environment=PATH=/home/<USER>/ai-brand-monitor/venv/bin
ExecStart=/home/<USER>/ai-brand-monitor/venv/bin/python -m app.main
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

Create `/etc/systemd/system/ai-brand-monitor-dashboard.service`:
```ini
[Unit]
Description=AI Brand Monitor Dashboard
After=network.target ai-brand-monitor.service

[Service]
Type=simple
User=aimonitor
WorkingDirectory=/home/<USER>/ai-brand-monitor
Environment=PATH=/home/<USER>/ai-brand-monitor/venv/bin
ExecStart=/home/<USER>/ai-brand-monitor/venv/bin/streamlit run app/web/dashboard.py --server.port=8501 --server.address=0.0.0.0
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

6. **Start services**:
```bash
sudo systemctl daemon-reload
sudo systemctl enable ai-brand-monitor ai-brand-monitor-dashboard
sudo systemctl start ai-brand-monitor ai-brand-monitor-dashboard
```

### 3. Cloud Deployment

#### AWS Deployment

1. **EC2 Setup**:
```bash
# Launch EC2 instance (t3.medium or larger)
# Configure security groups (ports 80, 443, 22)
# Attach IAM role with necessary permissions
```

2. **RDS Setup**:
```bash
# Create PostgreSQL RDS instance
# Configure security groups for database access
# Update DATABASE_URL in environment
```

3. **Load Balancer**:
```bash
# Create Application Load Balancer
# Configure target groups for API and dashboard
# Set up SSL certificate with ACM
```

#### Google Cloud Deployment

1. **Cloud Run Setup**:
```yaml
# cloudbuild.yaml
steps:
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/ai-brand-monitor', '.']
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/ai-brand-monitor']
  - name: 'gcr.io/cloud-builders/gcloud'
    args: ['run', 'deploy', 'ai-brand-monitor', '--image', 'gcr.io/$PROJECT_ID/ai-brand-monitor', '--platform', 'managed', '--region', 'us-central1']
```

2. **Cloud SQL**:
```bash
# Create PostgreSQL instance
gcloud sql instances create brand-monitor-db --database-version=POSTGRES_13 --tier=db-f1-micro --region=us-central1
```

## Configuration

### Environment Variables

#### Required Settings
```bash
# OpenRouter API
OPENROUTER_API_KEY=your-openrouter-api-key
OPENROUTER_DEFAULT_MODEL=openai/gpt-3.5-turbo

# Application
SECRET_KEY=your-production-secret-key-min-32-chars
API_HOST=0.0.0.0
API_PORT=8000
APP_URL=https://yourdomain.com

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/brand_monitor

# User Limits
FREE_TIER_DAILY_LIMIT=50
PREMIUM_TIER_DAILY_LIMIT=500
ENTERPRISE_TIER_DAILY_LIMIT=2000
```

#### Optional Settings
```bash
# Email notifications
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Scheduling
SCAN_SCHEDULE=0 9 * * *
REPORT_SCHEDULE=0 10 * * 1

# Logging
LOG_LEVEL=INFO
```

### Nginx Configuration

Create `/etc/nginx/sites-available/ai-brand-monitor`:
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;

    # API endpoints
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Dashboard
    location / {
        proxy_pass http://localhost:8501;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support for Streamlit
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
}
```

## Monitoring and Maintenance

### Health Checks

1. **API Health**:
```bash
curl https://yourdomain.com/api/v1/health
```

2. **Database Health**:
```bash
# Check database connection
python -c "from app.database.database import get_db_session; print('DB OK' if get_db_session() else 'DB Error')"
```

3. **OpenRouter API**:
```bash
# Test API connection
curl -X POST https://yourdomain.com/api/v1/openrouter/test-connection \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"api_key": "your-openrouter-key"}'
```

### Logging

1. **Application Logs**:
```bash
# View API logs
tail -f logs/app.log

# View scheduler logs
tail -f logs/scheduler.log
```

2. **System Logs**:
```bash
# View service logs
sudo journalctl -u ai-brand-monitor -f
sudo journalctl -u ai-brand-monitor-dashboard -f
```

### Backup

1. **Database Backup**:
```bash
# PostgreSQL backup
pg_dump brand_monitor > backup_$(date +%Y%m%d).sql

# Automated backup script
#!/bin/bash
BACKUP_DIR="/home/<USER>/backups"
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump brand_monitor | gzip > $BACKUP_DIR/backup_$DATE.sql.gz
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +7 -delete
```

2. **Application Data**:
```bash
# Backup data directory
tar -czf data_backup_$(date +%Y%m%d).tar.gz data/
```

### Updates

1. **Application Updates**:
```bash
# Pull latest changes
git pull origin main

# Update dependencies
pip install -r requirements.txt

# Run migrations
python -m app.database.migrations

# Restart services
sudo systemctl restart ai-brand-monitor ai-brand-monitor-dashboard
```

2. **Security Updates**:
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Update Python packages
pip list --outdated
pip install --upgrade package_name
```

## Troubleshooting

### Common Issues

1. **OpenRouter API Errors**:
   - Check API key validity
   - Verify account credits
   - Check rate limits

2. **Database Connection Issues**:
   - Verify DATABASE_URL
   - Check PostgreSQL service status
   - Verify user permissions

3. **High API Costs**:
   - Review usage statistics
   - Adjust user limits
   - Use cheaper models for routine monitoring

4. **Performance Issues**:
   - Monitor CPU and memory usage
   - Check database query performance
   - Review API response times

### Support

For deployment issues:
1. Check application logs
2. Verify configuration settings
3. Test individual components
4. Contact support with specific error details

## Security Considerations

### API Security
- Use strong SECRET_KEY (32+ characters)
- Enable HTTPS in production
- Implement rate limiting
- Regular security updates

### Database Security
- Use strong database passwords
- Restrict database access
- Regular backups
- Enable SSL connections

### OpenRouter API
- Secure API key storage
- Monitor usage and costs
- Set appropriate limits
- Regular key rotation
