# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
SECRET_KEY=your-secret-key-change-in-production

# Database Configuration
DATABASE_URL=sqlite:///./data/brand_monitor.db

# Scraping Configuration
SCRAPING_DELAY=5
MAX_RETRIES=3

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# AI Platform Credentials (optional)
CHATGPT_EMAIL=<EMAIL>
CHATGPT_PASSWORD=your-chatgpt-password

CLAUDE_EMAIL=<EMAIL>
CLAUDE_PASSWORD=your-claude-password

# OpenRouter API Configuration
OPENROUTER_API_KEY=your-openrouter-api-key
OPENROUTER_DEFAULT_MODEL=openai/gpt-3.5-turbo
OPENROUTER_RATE_LIMIT=60
OPENROUTER_MAX_TOKENS=1000
OPENROUTER_TEMPERATURE=0.7
APP_URL=http://localhost:8000

# User API Limits
FREE_TIER_DAILY_LIMIT=50
PREMIUM_TIER_DAILY_LIMIT=500
ENTERPRISE_TIER_DAILY_LIMIT=2000

# Scheduling
SCAN_SCHEDULE=0 9 * * *
REPORT_SCHEDULE=0 10 * * 1

# Logging
LOG_LEVEL=INFO
