#!/usr/bin/env python3
"""
AI Brand Monitor Setup Script
Initializes the database and creates sample data
"""

import sys
import os
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from app.database.database import init_db
from app.database.migrations import create_initial_data
from app.config import config

def setup_directories():
    """Create necessary directories"""
    print("Creating directories...")
    
    directories = [
        config.DATA_DIR,
        config.LOG_DIR,
        Path("static"),
        Path("tests")
    ]
    
    for directory in directories:
        directory.mkdir(exist_ok=True)
        print(f"✓ Created {directory}")

def setup_database():
    """Initialize database and create sample data"""
    print("\nInitializing database...")
    
    try:
        init_db()
        print("✓ Database tables created")
        
        create_initial_data()
        print("✓ Sample data created")
        
    except Exception as e:
        print(f"✗ Database setup failed: {e}")
        return False
    
    return True

def setup_environment():
    """Check and setup environment"""
    print("\nChecking environment...")
    
    env_file = Path(".env")
    if not env_file.exists():
        print("⚠ .env file not found. Copying from .env.example")
        example_file = Path(".env.example")
        if example_file.exists():
            import shutil
            shutil.copy(example_file, env_file)
            print("✓ .env file created from template")
            print("⚠ Please edit .env file with your configuration")
        else:
            print("✗ .env.example file not found")
            return False
    else:
        print("✓ .env file exists")
    
    return True

def check_dependencies():
    """Check if required dependencies are installed"""
    print("\nChecking dependencies...")
    
    required_packages = [
        'fastapi',
        'uvicorn',
        'sqlalchemy',
        'selenium',
        'streamlit',
        'spacy',
        'textblob',
        'pandas',
        'plotly'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠ Missing packages: {', '.join(missing_packages)}")
        print("Run: pip install -r requirements.txt")
        return False
    
    # Check spaCy model
    try:
        import spacy
        nlp = spacy.load("en_core_web_sm")
        print("✓ spaCy English model")
    except OSError:
        print("✗ spaCy English model")
        print("Run: python -m spacy download en_core_web_sm")
        return False
    
    return True

def main():
    """Main setup function"""
    print("🚀 AI Brand Monitor Setup")
    print("=" * 40)
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Setup failed: Missing dependencies")
        sys.exit(1)
    
    # Setup environment
    if not setup_environment():
        print("\n❌ Setup failed: Environment configuration")
        sys.exit(1)
    
    # Create directories
    setup_directories()
    
    # Setup database
    if not setup_database():
        print("\n❌ Setup failed: Database initialization")
        sys.exit(1)
    
    print("\n" + "=" * 40)
    print("✅ Setup completed successfully!")
    print("\nNext steps:")
    print("1. Edit .env file with your configuration")
    print("2. Start the API server: python -m app.main")
    print("3. Start the dashboard: streamlit run app/web/dashboard.py")
    print("4. Access the application:")
    print("   - API: http://localhost:8000")
    print("   - Dashboard: http://localhost:8501")
    print("   - API Docs: http://localhost:8000/docs")
    print("\nDefault admin credentials:")
    print("   Email: <EMAIL>")
    print("   Password: admin123")
    print("\n🎉 Happy monitoring!")

if __name__ == "__main__":
    main()
