# AI Brand Monitor - Installation Guide

## Quick Start

### 1. Prerequisites
- Python 3.11 or higher
- Chrome browser (for web scraping)
- Git

### 2. Installation Steps

```bash
# Clone the repository (if from git)
git clone <repository-url>
cd ai-brand-monitor

# Create virtual environment
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Download spaCy model
python -m spacy download en_core_web_sm

# Run setup
python setup.py

# Start the application
python run.py
```

### 3. Access the Application

- **API Server**: http://localhost:8000
- **Dashboard**: http://localhost:8501  
- **API Documentation**: http://localhost:8000/docs

### 4. Default Credentials

- **Email**: <EMAIL>
- **Password**: admin123

## Docker Installation

### Quick Start with Docker

```bash
# Build and start services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Access URLs (Docker)
- **API Server**: http://localhost:8000
- **Dashboard**: http://localhost:8501

## Manual Configuration

### Environment Variables

Copy `.env.example` to `.env` and configure:

```bash
cp .env.example .env
```

Edit `.env` with your settings:

```env
# API Configuration
SECRET_KEY=your-secret-key-here
API_HOST=0.0.0.0
API_PORT=8000

# Email Configuration (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# AI Platform Credentials (optional)
CHATGPT_EMAIL=your-chatgpt-email
CHATGPT_PASSWORD=your-chatgpt-password
```

### Database Setup

The application uses SQLite by default. For production, consider PostgreSQL:

```env
DATABASE_URL=postgresql://user:password@localhost/ai_brand_monitor
```

## Troubleshooting

### Common Issues

1. **Chrome/ChromeDriver Issues**
   ```bash
   # Install Chrome on Ubuntu/Debian
   wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
   sudo sh -c 'echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list'
   sudo apt-get update
   sudo apt-get install google-chrome-stable
   ```

2. **spaCy Model Missing**
   ```bash
   python -m spacy download en_core_web_sm
   ```

3. **Port Already in Use**
   ```bash
   # Change ports in .env file
   API_PORT=8001
   # Or kill existing processes
   lsof -ti:8000 | xargs kill -9
   ```

4. **Permission Errors**
   ```bash
   chmod +x setup.py run.py
   ```

### Development Setup

For development with hot reload:

```bash
# Terminal 1: API Server
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Terminal 2: Dashboard
streamlit run app/web/dashboard.py --server.port 8501
```

### Testing

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app

# Run specific test
pytest tests/test_api.py -v
```

## Production Deployment

### Using Docker in Production

1. **Create production docker-compose**:
```yaml
version: '3.8'
services:
  ai-brand-monitor:
    build: .
    ports:
      - "8000:8000"
    environment:
      - SECRET_KEY=your-production-secret-key
      - DATABASE_URL=******************************/ai_brand_monitor
    depends_on:
      - db
  
  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=ai_brand_monitor
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

2. **Set up reverse proxy (Nginx)**:
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location / {
        proxy_pass http://localhost:8501;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### Security Considerations

1. **Change default credentials**
2. **Use strong SECRET_KEY**
3. **Enable HTTPS in production**
4. **Configure CORS properly**
5. **Use environment variables for secrets**
6. **Regular security updates**

## Support

If you encounter issues:

1. Check the logs in the `logs/` directory
2. Verify all dependencies are installed
3. Ensure Chrome browser is available
4. Check port availability
5. Review environment configuration

For additional help, refer to the main README.md file.
